import { save<PERSON><PERSON><PERSON><PERSON>, saveZphyMemberLevel, getFuluList } from '@/api/zphy';
import { memberUpdateRenovation, memberGetHasSendTotalCount } from '@/api/firmus';
import LzImageSelector from '@/components/LzImageSelector';
import LzColorPicker from '@/components/LzColorPicker';
import LzTipPanel from '@/components/LzTipPanel';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../../../decorate/compoonets/EditHotZone';
import { checkUrl } from '@/pages/mengniu/utils';
import {
  Box,
  Button,
  Card,
  Divider,
  Field,
  Form,
  Input,
  Message,
  NumberPicker,
  Radio,
  Switch,
  Table,
  Checkbox,
  Pagination,
  Dialog,
} from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';
import ChoosePrize from '@/components/ChoosePrizeForDZ';

const formItemLayout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
  labelAlign: 'left',
  colon: true,
};

const validateNavUrl = (rule, value, callback) => {
  if (!value) {
    callback();
  } else {
    try {
      if (checkUrl(value)) {
        callback(`链接必须以 jd.com 或 isvjcloud.com 结尾`);
      } else {
        callback();
      }
    } catch (error) {
      callback(`请输入有效的 URL`);
    }
  }
};

function MemberCard({ data, dispatch, allJson, defaultData }) {
  const [pageLoading, setPageLoading] = React.useState(false);
  const [keyName, setKeyName] = React.useState('');
  const [imgName, setImgName] = React.useState('');
  const [hotVisible, setHotVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [prizeListFulu, setPrizeListFulu] = React.useState<any>([]);
  const [activeIndex, setActiveIndex] = React.useState(0);
  const [pagePrizeFuluList, setPagePrizeFuluList] = React.useState<any>([]);
  const [total, setTotal] = React.useState<any>(0);

  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  React.useEffect(() => {}, []);
  const field = Field.useField();
  const getPrizeList = async (page) => {
    if (page) {
      setPagePrizeFuluList(prizeListFulu.slice((page - 1) * 10, page * 10));
    } else {
      setLoading(true);
      const res = await getFuluList({ source: 'fh' });
      setPrizeListFulu(res);
      setTotal(res.length);
      setPagePrizeFuluList(res.slice(0, 10));
      setLoading(false);
    }
  };
  const updateJson: any = {};
  const addRepeatHotZone = () => {
    if (!data.membershipCard.repeatBg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setKeyName('hotDataList');
    setImgName('repeatBg');
    setHotVisible(true);
  };

  const deleteRepeatHotZoneUrl = (hotZoneIndex) => {
    data.membershipCard.hotDataList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };
  const isValidMemberLevels = (levels) => {
    const res = levels.filter((v, i) => i > 2 && v.minCans <= levels[i - 1].minCans);
    if (res.length > 0) {
      return false;
    }
    return true;
  };
  const saveSetting1 = (): any => {
    // 校验所有等级活动项图片和奖品
    for (const level of data.activityConfig) {
      for (const act of level.activityList || []) {
        if (!act.showImage) {
          Message.error('所有活动项必须上传图片');
          return;
        }
        if ((act.activityType === 1 || act.activityType === 2) && (!act.prizeList || act.prizeList.length === 0)) {
          Message.error('所有活动项至少要有一条奖品数据');
          return;
        }

        // 检查奖品数量是否超过限制
        if (act.activityType === 1 && act.prizeList && act.prizeList.length > 6) {
          Message.error(`等级${level.level}的升级礼活动项奖品数量不能超过6个`);
          return;
        }
        if (act.activityType === 2 && act.prizeList && act.prizeList.length > 10) {
          Message.error(`等级${level.level}的等级券包活动项奖品数量不能超过10个`);
          return;
        }
      }
    }
    updateJson.announcementPop = JSON.stringify(data.announcementPop);
    updateJson.membershipCard = JSON.stringify(data.membershipCard);
    updateJson.activityConfig = JSON.stringify(data.activityConfig);
    setPageLoading(true);
    memberUpdateRenovation(updateJson)
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const saveSetting2 = (): any => {
    if (!isValidMemberLevels(data.membershipCard.memberLevels)) {
      Message.error('请检查会员等级金额,高等级起始金额不能低于低等级起始金额');
      return false;
    }
    saveZphyMemberLevel({ memberLevelRequests: data.membershipCard.memberLevels }).then((res) => {
      Message.success('保存成功');
      dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
    });
  };
  const commonProps = {
    title: '会员卡',
    extra: (
      <div>
        {/* <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button> */}
      </div>
    ),
  };

  const calcPotNumber = (index) => {
    if (index == data.membershipCard.memberLevels.length - 1) {
      return '';
    }
    return `< ${Number(data.membershipCard.memberLevels[index + 1].minCans)}`;
  };
  return (
    <div>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header
          title="公告弹窗"
          extra={
            <Box direction="row" align="center">
              {/* <LzTipPanel
                warning
                // message="注：本弹窗不区分卓睿非卓睿页面，一经修改，保存发布后在所有页面生效"
                style={{
                  marginBottom: 0,
                  marginRight: 20,
                }}
              /> */}
              <Button
                type="primary"
                onClick={() => {
                  field.validate((errors, values) => {
                    if (errors) {
                      return;
                    }
                    saveSetting1();
                  });
                }}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          <Form field={field} {...formItemLayout}>
            <Form.Item
              label="是否弹出"
              extra={
                <div className="next-form-item-extra">
                  注：选择弹出，每次进入会员中心，弹出该弹窗（切换卓睿/非卓睿时不会弹出）
                </div>
              }
            >
              <Switch
                checked={data.announcementPop.showAd}
                onChange={(value) => {
                  setData({
                    announcementPop: {
                      ...data.announcementPop,
                      showAd: value,
                    },
                  });
                }}
              />
            </Form.Item>
            <Form.Item label="图片上传">
              <Box>
                <Box direction="row" align="center">
                  <LzImageSelector
                    value={data.announcementPop.adImgUrl}
                    onChange={(value) => {
                      setData({
                        announcementPop: {
                          ...data.announcementPop,
                          adImgUrl: value,
                        },
                      });
                    }}
                  />
                  <div className={styles.tip}>
                    {/* <div>图片尺寸：建议宽度为707px，高度为401px</div> */}
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </Box>
              </Box>
            </Form.Item>
            <Form.Item
              name="adLink"
              label="配置链接"
              validator={(rule, value, callback) => validateNavUrl(rule, value, callback)}
            >
              <Input
                value={data.announcementPop.adLink}
                trim
                placeholder="请输入跳转链接"
                onChange={(value) => {
                  setData({
                    announcementPop: {
                      ...data.adDialog,
                      adLink: value,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form>
        </Card.Content>
      </Card>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header title="等级配置" />
        <Divider />
        <Card.Content>
          <div className={styles.memberContainer}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div className="crm-label">等级对应金额设置</div>
              <Button type="primary" onClick={saveSetting2}>
                更新至当前设置
              </Button>
            </div>
            <div style={{ color: '#e75050' }}>
              <div>*等级金额档位修改后生效时间：</div>
              <div>
                *0点-12点进行修改，保存后当日16点后全部会员生效；12点-24点进行修改，保存后次日4点后全部会员生效。
              </div>
              <div>生效期间，会员分批变化等级，期间领取等级礼以当时等级为准。建议根据以上规则修改前进行公告。</div>
            </div>
            <div className={styles.levelSetting}>
              <Table dataSource={data.membershipCard.memberLevels} style={{ marginTop: '15px' }} loading={loading}>
                <Table.Column title="等级" align={'center'} cell={(val, index, info) => <span>LV{info.level}</span>} />
                <Table.Column
                  title="描述及当前设置"
                  align={'center'}
                  cell={(_, index, row) => {
                    return (
                      <div>
                        {row.level == 0 ? (
                          <div>新客未购</div>
                        ) : row.level == 1 ? (
                          <div>只购买过中小罐未购买正装的会员</div>
                        ) : (
                          <div className={styles['flex-column']}>
                            <div style={{ color: '#1677ff' }}>{row.minCans}&nbsp;</div>
                            <div>{`≤ 正装金额`}&nbsp;</div>
                            <div>{calcPotNumber(index)}</div>
                          </div>
                        )}
                      </div>
                    );
                  }}
                />
                <Table.Column
                  title="修改等级起始金额"
                  align={'center'}
                  cell={(_, index, row) => (
                    <div>
                      {row.level < 3 ? (
                        <div />
                      ) : (
                        <NumberPicker
                          style={{ width: '150px' }}
                          value={row.minCans}
                          onChange={(v) => {
                            if (!v) {
                              row.minCans = 2;
                            } else {
                              row.minCans = v;
                            }
                            const updatedData = { ...data };
                            updatedData.membershipCard.memberLevels[index] = row;
                            if (!isValidMemberLevels(updatedData.membershipCard.memberLevels)) {
                              Message.error('请确保等级金额从大到小排列');
                              return;
                            }
                            setData(updatedData);
                          }}
                          type="inline"
                          min={2}
                          max={9999999}
                        />
                      )}
                    </div>
                  )}
                />
              </Table>
            </div>
          </div>
        </Card.Content>
      </Card>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header
          {...commonProps}
          extra={
            <Box direction="row" align="center">
              <Button
                type="primary"
                onClick={() => {
                  saveSetting1();
                }}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.bgContainer}>
              <div className="crm-label">kv设置</div>
              <div className={styles.operation}>
                <div className={styles.MemberContainer}>
                  <div className={styles.imgUpload}>
                    <div>
                      <LzImageSelector
                        bgWidth={266}
                        bgHeight={150}
                        value={data.membershipCard.repeatBg}
                        onChange={(repeatBg) => {
                          const updatedData = { ...data };
                          updatedData.membershipCard.repeatBg = repeatBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        {/* <div>图片尺寸：建议宽度为707px，高度为401px</div> */}
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                    <div className={styles.setting}>
                      <Button className={styles.btn} onClick={() => addRepeatHotZone()}>
                        添加热区
                      </Button>
                      {Array.isArray(data.membershipCard.hotDataList) &&
                        data.membershipCard.hotDataList.map((hot, hotZoneIndex) => (
                          <div key={hotZoneIndex} className={styles.urlContainer}>
                            <div className={styles.url}>{`热区${String(hotZoneIndex + 1).padStart(2, '0')}:${
                              hot.url
                            }`}</div>
                            <i
                              className="iconfont icon-icon-07 btn-del"
                              onClick={() => deleteRepeatHotZoneUrl(hotZoneIndex)}
                            />
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.kvContainer}>
              <div className="crm-label">卡面背景</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  bgWidth={266}
                  bgHeight={150}
                  value={data.membershipCard.moduleBgImg}
                  onChange={(moduleBgImg) => {
                    const updatedData = { ...data };
                    updatedData.membershipCard.moduleBgImg = moduleBgImg;
                    setData(updatedData);
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：宽度建议为750px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
            </div>
            <div className={styles.colorContainer}>
              <div className="crm-label">文字颜色设置</div>
              <div className={styles.colorPicker}>
                <div className={styles.colorItem}>
                  <span>用户等级颜色:</span>
                  <LzColorPicker
                    value={data.membershipCard.levelColor}
                    onChange={(levelColor) => {
                      const updatedData = { ...data };
                      updatedData.membershipCard.levelColor = levelColor;
                      setData(updatedData);
                    }}
                  />
                </div>
                <div className={styles.colorItem}>
                  <span>用户昵称颜色:</span>
                  <LzColorPicker
                    value={data.membershipCard.nickColor}
                    onChange={(nickColor) => {
                      const updatedData = { ...data };
                      updatedData.membershipCard.nickColor = nickColor;
                      setData(updatedData);
                    }}
                  />
                </div>
                <div className={styles.colorItem}>
                  <span>卡面文字颜色:</span>
                  <LzColorPicker
                    value={data.membershipCard.textColor}
                    onChange={(textColor) => {
                      const updatedData = { ...data };
                      updatedData.membershipCard.textColor = textColor;
                      setData(updatedData);
                    }}
                  />
                </div>
              </div>
            </div>
            <div className={styles.popupContainer}>
              <div className="crm-label">提示弹窗</div>
              <LzImageSelector
                bgWidth={266}
                bgHeight={150}
                value={data.membershipCard.popupBgImg}
                onChange={(popupBgImg) => {
                  const updatedData = { ...data };
                  updatedData.membershipCard.popupBgImg = popupBgImg;
                  setData(updatedData);
                }}
              />
              <div className={styles.tip}>
                <div>图片尺寸：宽度建议为650px,支持jpg、jpeg、png格式，大小不超过1M</div>
              </div>
            </div>
            <div className={styles.btnContainer}>
              <div className="crm-label">标题配置</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  bgWidth={266}
                  bgHeight={100}
                  value={data.membershipCard.titleImg}
                  onChange={(titleImg) => {
                    const updatedData = { ...data };
                    updatedData.membershipCard.titleImg = titleImg;
                    setData(updatedData);
                  }}
                />
              </div>
              <div className={styles.btnLink}>
                标题链接：
                <Input
                  value={data.membershipCard.titleLink}
                  trim
                  placeholder="请输入跳转链接"
                  onChange={(value) => {
                    const updatedData = { ...data };
                    updatedData.membershipCard.titleLink = value;
                    setData(updatedData);
                  }}
                />
              </div>
            </div>
            <div className={styles.btnContainer}>
              <div className="crm-label">跳转按钮配置</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  bgWidth={266}
                  bgHeight={100}
                  value={data.membershipCard.btnImg}
                  onChange={(btnImg) => {
                    const updatedData = { ...data };
                    updatedData.membershipCard.btnImg = btnImg;
                    setData(updatedData);
                  }}
                />
              </div>
              <div className={styles.btnLink}>
                按钮链接：
                <Input
                  value={data.membershipCard.btnLink}
                  trim
                  placeholder="请输入跳转链接"
                  onChange={(value) => {
                    const updatedData = { ...data };
                    updatedData.membershipCard.btnLink = value;
                    setData(updatedData);
                  }}
                />
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
      <Card free>
        <Card.Header
          title="活动配置"
          extra={
            <Box direction="row" align="center">
              <Button
                type="primary"
                onClick={() => {
                  saveSetting1();
                }}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          {(() => {
            const tabList = [
              { key: 0, label: 'LV.0' },
              { key: 1, label: 'LV.1' },
              { key: 2, label: 'LV.2' },
              { key: 3, label: 'LV.3' },
              { key: 4, label: 'LV.4' },
              { key: 5, label: 'LV.5' },
            ];

            // 初始化activityConfig，确保每个等级都有默认的空数据
            const initActivityConfig = () => {
              const config = data?.activityConfig || [];
              const initializedConfig = tabList.map((tab) => {
                const existingLevel = config.find((item) => item.level === tab.key);
                if (existingLevel) {
                  // 如果已存在该等级的配置，确保至少有一条数据
                  return {
                    ...existingLevel,
                    activityList:
                      existingLevel.activityList && existingLevel.activityList.length > 0
                        ? existingLevel.activityList
                        : [
                            {
                              activityType: 1,
                              showType: 1,
                              showImage: '',
                              linkUrl: '',
                              prizeList: [],
                              isShow: tab.key === 0, // LV0默认为true，其他为false
                            },
                          ],
                    isShow: tab.key === 0, // LV0默认为true，其他为false
                  };
                } else {
                  // 如果不存在该等级的配置，创建默认配置
                  return {
                    level: tab.key,
                    description: '',
                    isShow: tab.key === 0, // LV0默认为true，其他为false
                    activityList: [
                      {
                        activityType: 1,
                        showType: 1,
                        showImage: '',
                        linkUrl: '',
                        prizeList: [],
                        isShow: tab.key === 0, // LV0默认为true，其他为false
                      },
                    ],
                  };
                }
              });
              return initializedConfig;
            };

            const [activityConfig, setActivityConfig] = React.useState(initActivityConfig());
            const [activeTab, setActiveTab] = React.useState(0); // 显示的等级tab
            const [visible, setVisible] = React.useState(false); // 控制资产弹窗的显示和隐藏
            const [visibleFulu, setVisibleFulu] = React.useState(false); // 控制资产弹窗的显示和隐藏
            const [actIndex, setActIndex] = React.useState(0); // 活动索引
            const [target, setTarget] = React.useState(0); // 资产索引
            const [editValue, setEditValue] = React.useState(null); // 资产编辑的数据
            // Tab切换时，设置isShow
            const handleTabChange = (selectedTabIdx) => {
              const selectedLevel = tabList[selectedTabIdx].key;
              // 更新activityConfig中isShow字段
              const updatedActivityConfig = (data.activityConfig || []).map((item) => ({
                ...item,
                isShow: item.level === selectedLevel,
              }));
              setActiveTab(selectedTabIdx);
              setData({
                ...data,
                activityConfig: updatedActivityConfig,
              });
            };
            // 根据选中的 tab key 查找对应 level 的配置
            const currentLevel = activityConfig.find((item) => item.level === tabList[activeTab].key) || {
              level: tabList[activeTab].key,
              description: '',
              activityList: [],
            };
            const { activityList } = currentLevel;

            // 更新数据到父组件
            const updateData = (newConfig) => {
              setActivityConfig(newConfig);
              const updatedData = {
                ...data,
                activityConfig: newConfig,
              };
              setData(updatedData);
            };

            // 更新等级文案
            const updateDescription = (description) => {
              const newConfig = activityConfig.map((config) =>
                config.level === tabList[activeTab].key ? { ...config, description } : config,
              );
              updateData(newConfig);
            };

            // 添加活动项的函数
            const addActivity = () => {
              if (activityList.length >= 6) {
                Message.error('最多只能添加6条活动数据');
                return;
              }

              const newActivity = {
                activityType: null, // 修改为null，默认不选中任何选项
                showType: null,
                showImage: '',
                linkUrl: '',
                prizeList: [],
              };

              const newActivityList = [...activityList, newActivity];

              // 获取当前选中的等级
              const selectedLevel = tabList[activeTab].key;

              // 更新所有等级的isShow属性
              const newConfig = activityConfig.map((config) => {
                if (config.level === selectedLevel) {
                  // 当前选中的等级，设置isShow为true并更新activityList
                  return {
                    ...config,
                    isShow: true,
                    activityList: newActivityList,
                  };
                } else {
                  // 其他等级，设置isShow为false
                  return {
                    ...config,
                    isShow: false,
                  };
                }
              });

              updateData(newConfig);
            };

            // 移动活动项的函数
            const moveActivity = (idx, direction) => {
              const newList = [...activityList];
              if (direction === 'up' && idx > 0) {
                [newList[idx - 1], newList[idx]] = [newList[idx], newList[idx - 1]];
              } else if (direction === 'down' && idx < newList.length - 1) {
                [newList[idx], newList[idx + 1]] = [newList[idx + 1], newList[idx]];
              }
              // 更新当前等级的 activityList
              const newConfig = activityConfig.map((config) =>
                config.level === tabList[activeTab].key ? { ...config, activityList: newList } : config,
              );
              updateData(newConfig);
            };

            // 删除活动项的函数
            const deleteActivity = (index) => {
              if (activityList.length <= 1) {
                Message.error('至少需要保留一条活动数据');
                return;
              }

              const newActivityList = activityList.filter((_, idx) => idx !== index);
              const newConfig = activityConfig.map((config) =>
                config.level === tabList[activeTab].key ? { ...config, activityList: newActivityList } : config,
              );

              updateData(newConfig);
            };

            // 更新活动项
            const updateActivity = (index, field, value) => {
              const newActivityList = activityList.map((item, idx) =>
                idx === index ? { ...item, [field]: value } : item,
              );
              const newConfig = activityConfig.map((config) =>
                config.level === tabList[activeTab].key ? { ...config, activityList: newActivityList } : config,
              );
              updateData(newConfig);
            };

            const onPrizeChange = async (data) => {
              if (!editValue) {
                // 检查奖品是否已存在
                for (let i = 0; i < activityConfig.length; i++) {
                  for (let j = 0; j < activityConfig[i].activityList.length; j++) {
                    const prizeList = activityConfig[i].activityList[j].prizeList || [];
                    for (let k = 0; k < prizeList.length; k++) {
                      if (prizeList[k].prizeKey === data.prizeKey) {
                        Message.error('该奖品已存在');
                        return;
                      }
                    }
                  }
                }

                // 调用接口查询历史发放数量
                try {
                  const hasSendTotalCount = await memberGetHasSendTotalCount({
                    prizeKey: data.prizeKey,
                  });

                  // 新增奖品
                  const prizeList = [...(activityConfig[activeTab].activityList[actIndex].prizeList || [])];
                  const newPrize = {
                    itemId: data.itemId || '1',
                    ...data,
                    hasSendTotalCount: hasSendTotalCount ? Number(hasSendTotalCount) : 0,
                  };

                  // 验证发放份数必须大于历史发放份数
                  if (newPrize.hasSendTotalCount > 0 && data.sendTotalCount < newPrize.hasSendTotalCount) {
                    Message.error(`发放份数不能小于历史发放份数：${newPrize.hasSendTotalCount}`);
                    return;
                  }

                  // 如果是等级券包类型，默认勾选当前等级
                  if (activityConfig[activeTab].activityList[actIndex].activityType === 2 && !newPrize.canLevel) {
                    newPrize.canLevel = [tabList[activeTab].key];
                  }

                  // 将新奖品添加到列表末尾
                  prizeList.push(newPrize);
                  updateActivity(actIndex, 'prizeList', prizeList);
                  setVisible(false);
                } catch (error) {
                  console.error('获取历史发放数量失败', error);
                  Message.error('获取历史发放数量失败');
                }
              } else {
                // 验证发放份数必须大于历史发放份数
                if (editValue.hasSendTotalCount && data.sendTotalCount < editValue.hasSendTotalCount) {
                  Message.error(`发放份数不能小于历史发放份数：${editValue.hasSendTotalCount}`);
                  return;
                }

                // 编辑现有奖品时，所有已勾选等级的同 prizeKey 奖品同步更新
                const newActivityConfig = activityConfig.map((cfg) => {
                  const newActivityList = (cfg.activityList || []).map((act) => {
                    if (act.activityType === 2 && act.prizeList) {
                      const newPrizeList = act.prizeList.map((p) => {
                        // 只要是同 prizeKey 且 canLevel 包含当前等级，都同步为最新数据
                        if (p.prizeKey === data.prizeKey && (p.canLevel || []).includes(cfg.level)) {
                          return { ...p, ...data };
                        }
                        return p;
                      });
                      return { ...act, prizeList: newPrizeList };
                    }
                    if (act.activityType === 1 && act.prizeList) {
                      return {
                        ...act,
                        prizeList: act.prizeList.map((p) => {
                          if (p.prizeKey === data.prizeKey) {
                            return { ...p, ...data };
                          }
                          return p;
                        }),
                      };
                    }
                    return act;
                  });
                  return { ...cfg, activityList: newActivityList };
                });
                updateData(newActivityConfig);
                setVisible(false);
              }
            };

            return (
              <div>
                {/* Tab栏 */}
                <div style={{ display: 'flex', gap: 24, marginBottom: 16 }}>
                  {tabList.map((tab, idx) => (
                    <span
                      key={tab.key}
                      style={{
                        color: idx === activeTab ? '#1677ff' : '#333',
                        fontWeight: idx === activeTab ? 700 : 400,
                        borderBottom: idx === activeTab ? '2px solid #1677ff' : 'none',
                        cursor: 'pointer',
                        fontSize: 16,
                        padding: '0 8px',
                      }}
                      onClick={() => handleTabChange(idx)}
                    >
                      {tab.label}
                    </span>
                  ))}
                </div>

                {/* 等级文案输入 */}
                {[0, 1, 5].includes(tabList[activeTab].key) && (
                  <div style={{ marginBottom: 16 }}>
                    <span style={{ fontWeight: 600, marginRight: 16 }}>等级文案：</span>
                    <Input
                      style={{ width: 300 }}
                      value={currentLevel.description}
                      placeholder="请输入等级文案"
                      maxLength={25}
                      onChange={(v) => {
                        updateDescription(v);
                      }}
                    />
                  </div>
                )}

                {/* 添加按钮和计数 */}
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                  <Button type="primary" size="small" onClick={addActivity} disabled={activityList.length >= 6}>
                    添加 ({activityList.length}/6)
                  </Button>
                  <span style={{ color: '#ff4d4f', marginLeft: 8 }}>
                    0级不能添加升级礼，每个等级仅能添加一次该活动类型
                  </span>
                </div>

                {/* 当前Tab下的活动内容 */}
                {activityList.map((item, idx) => (
                  <div
                    key={idx}
                    style={{
                      background: '#fff',
                      borderRadius: 8,
                      marginBottom: 24,
                      boxShadow: '0 2px 8px #f0f1f2',
                      padding: 20,
                      position: 'relative',
                    }}
                  >
                    {/* 右上角操作按钮 */}
                    <div style={{ position: 'absolute', top: 16, right: 16, display: 'flex', gap: 8 }}>
                      <Button
                        text
                        style={{ color: '#1677ff' }}
                        onClick={() => moveActivity(idx, 'up')}
                        disabled={idx === 0}
                      >
                        ↑
                      </Button>
                      <Button
                        text
                        style={{ color: '#1677ff' }}
                        onClick={() => moveActivity(idx, 'down')}
                        disabled={idx === activityList.length - 1}
                      >
                        ↓
                      </Button>
                      <Button
                        text
                        style={{ color: '#1677ff' }}
                        onClick={() => deleteActivity(idx)}
                        disabled={activityList.length < 1}
                      >
                        🗑
                      </Button>
                    </div>

                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>活动类型：</span>
                      <Radio.Group
                        value={item.activityType}
                        onChange={(v) => {
                          // 当有奖品数据时，不允许切换活动类型
                          if (item.prizeList && item.prizeList.length > 0 && item.activityType !== v) {
                            Message.error('当前有奖品数据，请先删除所有奖品后再切换活动类型');
                            return;
                          }
                          // 只有在没有奖品数据或者选择当前活动类型时才允许更新
                          updateActivity(idx, 'activityType', v);
                        }}
                      >
                        <Radio
                          value={1}
                          disabled={
                            tabList[activeTab].key === 0 ||
                            activityList.some((act) => act.activityType === 1 && act !== item)
                          }
                        >
                          升级礼
                        </Radio>
                        <Radio value={2} disabled={activityList.some((act) => act.activityType === 2 && act !== item)}>
                          等级券包
                        </Radio>
                        <Radio value={3}>活动跳转</Radio>
                      </Radio.Group>
                    </div>

                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>显示类型：</span>
                      <Radio.Group value={item.showType} onChange={(v) => updateActivity(idx, 'showType', v)}>
                        <Radio value={1}>一排一</Radio>
                        <Radio value={2}>一排二</Radio>
                        <Radio value={3}>一排三</Radio>
                      </Radio.Group>
                    </div>

                    {/* 图片上传 */}
                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>图片上传：</span>
                      <LzImageSelector
                        bgWidth={266}
                        bgHeight={150}
                        width={item.showType === 1 ? 660 : item.showType === 2 ? 320 : 200}
                        height={300}
                        value={item.showImage}
                        onChange={(showImage) => {
                          if (!activityList) {
                            console.error('activityList is undefined');
                            return;
                          }
                          const newActivityList = activityList.map((act, actIdx) =>
                            actIdx === idx ? { ...act, showImage } : act,
                          );
                          // 创建新的配置对象，只更新当前等级的活动列表
                          const newConfig = activityConfig.map((config) =>
                            config.level === tabList[activeTab].key
                              ? { ...config, activityList: newActivityList }
                              : config,
                          );
                          updateData(newConfig);
                        }}
                      />
                      <span style={{ color: '#999' }}>
                        图片尺寸：
                        {item.showType === 1 && '660*300px'}
                        {item.showType === 2 && '320*300px'}
                        {item.showType === 3 && '200*300px'}
                        ，支持png、jpg、jpeg格式，大小不超过1M
                      </span>
                    </div>

                    {/* 配置链接 */}
                    {item.activityType === 3 && (
                      <div style={{ marginBottom: 16 }}>
                        <span style={{ fontWeight: 600, marginRight: 16 }}>配置链接：</span>
                        <Input
                          style={{ width: 300 }}
                          value={item.linkUrl}
                          placeholder="请输入跳转链接"
                          onChange={(v) => updateActivity(idx, 'linkUrl', v)}
                        />
                      </div>
                    )}

                    {/* 奖品来源配置 - 仅在升级礼和等级券包时显示 */}
                    {(item.activityType === 1 || item.activityType === 2) && (
                      <div style={{ marginBottom: 16 }}>
                        <Button
                          disabled={item.activityType === 1 ? item.prizeList.length >= 6 : item.prizeList.length >= 10}
                          onClick={() => {
                            setTarget(item.prizeList?.length || 0);
                            setActIndex(idx);
                            setEditValue(null);
                            setVisible(true);
                          }}
                        >
                          添加奖品 {item.prizeList.length}/{item.activityType === 1 ? 6 : 10}
                        </Button>
                        {/* 只在activityType为1时显示福禄奖品按钮 */}
                        {/* {item.activityType === 1 && (
                          <Button
                            disabled={item.prizeList.length >= 6}
                            onClick={() => {
                              setTarget(item.prizeList?.length || 0);
                              setActIndex(idx);
                              setEditValue(null);
                              setVisibleFulu(true);
                              getPrizeList();
                            }}
                            style={{ marginLeft: '8px' }}
                          >
                            添加福禄奖品
                          </Button>
                        )} */}
                        <span style={{ color: '#ff4d4f', marginLeft: 8 }}>至少保证有一个奖品</span>
                        {/* 升级礼使用表格展示 */}
                        {item.activityType === 1 && item.prizeList && item.prizeList.length > 0 && (
                          <Table dataSource={item.prizeList} style={{ marginLeft: '15px', marginTop: '10px' }}>
                            <Table.Column title="奖品名称" dataIndex="prizeName" />
                            <Table.Column
                              title="单位数量"
                              cell={(_, idx, row) => {
                                if (row.prizeType === 1) {
                                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                                } else {
                                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                                }
                              }}
                            />
                            <Table.Column
                              title="历史发放份数"
                              cell={(_, prizeIdx, row) => (
                                <div>{row.hasSendTotalCount ? `${row.hasSendTotalCount}份` : 0}</div>
                              )}
                            />
                            <Table.Column
                              title="发放份数"
                              cell={(_, prizeIdx, row) => (
                                <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : 0}</div>
                              )}
                            />
                            <Table.Column
                              title="奖品图"
                              cell={(_, prizeIdx, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                            />
                            <Table.Column
                              title="操作"
                              width={130}
                              cell={(val, prizeIdx, row) => (
                                <>
                                  <Button
                                    text
                                    type="primary"
                                    onClick={() => {
                                      setEditValue(row);
                                      setActIndex(idx);
                                      setTarget(prizeIdx);
                                      setVisible(true);
                                    }}
                                  >
                                    <i className="iconfont icon-bianji" /> 编辑
                                  </Button>
                                  <Button
                                    text
                                    type="primary"
                                    style={{ marginLeft: '5px' }}
                                    onClick={() => {
                                      Dialog.confirm({
                                        v2: true,
                                        title: '提示',
                                        centered: true,
                                        content: '确认删除该奖品吗？',
                                        onOk: () => {
                                          const newPrizeList = item.prizeList.filter((_, i) => i !== prizeIdx);
                                          updateActivity(idx, 'prizeList', newPrizeList);
                                        },
                                        onCancel: () => console.log('取消删除'),
                                      });
                                    }}
                                  >
                                    <i className="iconfont icon-shanchu" /> 删除
                                  </Button>
                                </>
                              )}
                            />
                          </Table>
                        )}

                        {/* 等级券包使用原有的卡片展示方式 */}
                        {item.activityType === 2 &&
                          item.prizeList &&
                          item.prizeList.map((prize, prizeIdx) => (
                            <div
                              key={prizeIdx}
                              style={{
                                border: '1px solid #d9d9d9',
                                borderRadius: 4,
                                padding: 12,
                                marginBottom: 8,
                                marginTop: 8,
                                position: 'relative',
                              }}
                            >
                              <Button
                                text
                                style={{ position: 'absolute', top: 8, right: 38, color: '#1677ff' }}
                                onClick={() => {
                                  setEditValue(prize);
                                  setActIndex(idx);
                                  setTarget(prizeIdx);
                                  setVisible(true);
                                }}
                              >
                                编辑
                              </Button>
                              <Button
                                text
                                style={{ position: 'absolute', top: 8, right: 8, color: '#1677ff' }}
                                onClick={() => {
                                  // 获取当前奖品的 canLevel
                                  const canLevelArr = prize.canLevel || [];
                                  // 遍历所有等级，删除所有已勾选等级下同 prizeKey 的奖品
                                  const newActivityConfig = activityConfig.map((cfg) => {
                                    const newActivityList = (cfg.activityList || []).map((act) => {
                                      if (act.activityType === 2 && act.prizeList) {
                                        const newPrizeList = act.prizeList.filter((p) => {
                                          // 只要是同 prizeKey 且 canLevel 有交集就删除
                                          if (p.prizeKey === prize.prizeKey && canLevelArr.includes(cfg.level)) {
                                            return false;
                                          }
                                          return true;
                                        });
                                        return { ...act, prizeList: newPrizeList };
                                      }
                                      return act;
                                    });
                                    return { ...cfg, activityList: newActivityList };
                                  });
                                  updateData(newActivityConfig);
                                }}
                              >
                                删除
                              </Button>
                              <div>计划ID：{prize.prizeKey || '5844559'}</div>
                              <div>奖品名称：{prize.prizeName || '满10减1000'}</div>
                              <div>历史发放份数：{prize.hasSendTotalCount || 0}</div>
                              <div>发放份数：{prize.sendTotalCount || '20'}</div>
                              <div>
                                可领取的等级设置：
                                <Checkbox.Group
                                  value={prize.canLevel}
                                  onChange={(checkedLevels) => {
                                    // prize: 当前奖品对象
                                    // checkedLevels: 当前所有勾选的等级key数组
                                    const updatedPrize = { ...prize };
                                    // 遍历所有等级，找到所有有等级券包活动的等级
                                    const newActivityConfig = activityConfig.map((cfg) => {
                                      // 找到等级券包活动
                                      const newActivityList = (cfg.activityList || []).map((act) => {
                                        if (act.activityType === 2) {
                                          // 判断当前等级是否在 checkedLevels
                                          if (checkedLevels.includes(cfg.level)) {
                                            // 查找该等级下是否已存在该奖品
                                            const existIdx = (act.prizeList || []).findIndex(
                                              (p) => p.prizeKey === prize.prizeKey,
                                            );
                                            const newPrizeList = act.prizeList ? [...act.prizeList] : [];
                                            if (existIdx > -1) {
                                              // 已存在则用最新数据覆盖（同步所有字段）
                                              newPrizeList[existIdx] = { ...updatedPrize, canLevel: checkedLevels };
                                            } else {
                                              // 不存在则新增
                                              newPrizeList.push({ ...updatedPrize, canLevel: checkedLevels });
                                            }
                                            return { ...act, prizeList: newPrizeList };
                                          } else if (act.prizeList) {
                                            return {
                                              ...act,
                                              prizeList: act.prizeList.filter((p) => p.prizeKey !== prize.prizeKey),
                                            };
                                          }
                                        }
                                        return act;
                                      });
                                      return { ...cfg, activityList: newActivityList };
                                    });
                                    updateData(newActivityConfig);
                                  }}
                                >
                                  {tabList.map((level) => (
                                    <Checkbox key={level.key} style={{ marginLeft: 8 }} value={level.key}>
                                      {level.label}
                                    </Checkbox>
                                  ))}
                                </Checkbox.Group>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                ))}

                <LzDialog
                  title={false}
                  visible={visible}
                  footer={false}
                  onClose={() => setVisible(false)}
                  style={{ width: '670px' }}
                >
                  <ChoosePrize
                    formData={activityConfig[activeTab]?.activityList[actIndex] || {}}
                    editValue={editValue}
                    defaultEditValue={
                      defaultData.memberCard?.activityConfig?.[activeTab]?.activityList?.[actIndex]?.prizeList?.[
                        target
                      ] || null
                    }
                    onChange={onPrizeChange}
                    onCancel={() => setVisible(false)}
                    hasProbability={false}
                    hasLimit={false}
                    typeList={activityConfig[activeTab]?.activityList?.[actIndex]?.activityType === 1 ? [3] : [1]}
                    defaultTarget={activityConfig[activeTab]?.activityList?.[actIndex]?.activityType === 1 ? 3 : 1}
                  />
                </LzDialog>
                <LzDialog
                  title={'选择奖品'}
                  visible={visibleFulu}
                  footer={false}
                  onClose={() => setVisibleFulu(false)}
                  style={{ width: '660px' }}
                >
                  <div>
                    <Table dataSource={pagePrizeFuluList} style={{ marginTop: '15px' }} loading={loading}>
                      <Table.Column title="序号" align={'center'} cell={(_, index, row) => <span>{index + 1}</span>} />
                      <Table.Column title="奖品" align={'center'} dataIndex="productName" />
                      <Table.Column
                        title="操作"
                        align={'center'}
                        cell={(_, index, row) => (
                          <Button
                            type={'primary'}
                            text
                            style={{ marginLeft: '10px' }}
                            onClick={() => {
                              console.log(activeIndex, row.productId);
                              data.levelGiftList[activeIndex].value = row.productId;
                              data.levelGiftList[activeIndex].fuluName = row.productName;
                              setVisibleFulu(false);
                            }}
                          >
                            选择
                          </Button>
                        )}
                      />
                    </Table>
                    <Pagination
                      total={total}
                      onChange={(v) => {
                        getPrizeList(v);
                      }}
                      className="page-demo"
                    />
                  </div>
                </LzDialog>
              </div>
            );
          })()}
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={hotVisible}
        footer={false}
        onClose={() => setHotVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          keyName={keyName}
          imgName={imgName}
          data={data.membershipCard}
          dataList={[data.membershipCard]}
          dataIndex={0}
          dispatch={({ type, payload }) => dispatch({ type, payload: payload[0] })}
          onClose={() => setHotVisible(false)}
        />
      </LzDialog>
    </div>
  );
}

export default MemberCard;
