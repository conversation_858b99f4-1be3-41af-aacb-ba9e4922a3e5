/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-26 10:19
 * Description:
 */
export interface Page {
  pageNo: number;
  pageSize: number;
}

export interface Thisresx {
  planName?: string;
  createTime?: string;
  startDate?: string;
  endDate?: string;
  planId?: number;
  prizeKey?: number;
  quantityRemain?: number;
  planStatus?: number;
}

export interface CreateJdBeanPlan {
  planContent: string;
  planName: string;
  putTime: any[];
  quantityTotal: string;
}

export interface CreateJdBeanPlanSubmitForm extends CreateJdBeanPlan {
  sendRule: number;
  // 京豆计划发放每人次限制
  sendTimesPerUser: number;
  startDate: string;
  endDate: string;
}

export interface CreateJdBeanPlanRule {
  field: string;
  trigger: string | undefined;
  validator: Function;
}

export interface ComponentProps {
  [propName: string]: any;
}
