/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, {Dayjs} from 'dayjs';
import {Message} from '@alifd/next';
import format from '@/utils/format';
import {getParams} from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import {getShop} from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  prizeKey: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface StepList {
  stepName: string;
  stepNum: string;
  prizeList: PrizeInfo[];
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  actStrategy: string;
  bannerList: any,
  bannerExampleImage: string;
  isShowBanner: boolean,
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  signStartTime: string;
  signEndTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  prizeDay: PrizeInfo[];
  limitOrder: number;
  orderStartTime: string;
  orderEndTime: string;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;

  type: number,
  days: number,
  isExposure: number,
  skuList: any[],
  exposureSkuList: any[],
  seriesList: any[],
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  1: '优惠券',
  3: '实物',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/316496/20/12887/97840/685e3d79Ff1bfdd26/ca08021c5b15fcdf.png',
  actBg: '', // 主页背景图
  actBgColor: '#e8e3da', // 主页背景色
  actStrategy: '//img10.360buyimg.com/imgzone/jfs/t1/306120/30/13408/72489/685b731fFc02185fa/801fc62d1ad771ae.png', // 活动攻略图
  isShowBanner: true,
  bannerExampleImage: '//img10.360buyimg.com/imgzone/jfs/t1/316008/37/12437/32852/685b9246F5e57622d/273d5e32d41aea15.png',
  bannerList: [{
    bannerImage: '//img10.360buyimg.com/imgzone/jfs/t1/316008/37/12437/32852/685b9246F5e57622d/273d5e32d41aea15.png',
    isShowJump: true,
    bannerLink: '',
  }],

  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `满罐阶梯礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 报名开始时间
    signStartTime: format.formatDateTimeDayjs(now),
    // 报名结束时间
    signEndTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    limitOrder: 1,
    orderStartTime: format.formatDateTimeDayjs(now),
    orderEndTime: format.formatDateTimeDayjs(after30),
    // 每日签到奖品
    prizeDay: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '满罐赢好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    // 模板code
    templateCode: '2001',
    // 会员等级标签
    gradeLabel: [],
    // 人群包
    crowdBag: '',

    type: 0,
    days: 0,
    isExposure: 1,
    skuList: [],
    exposureSkuList: [],
    seriesList: [],
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
  prizeKey: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag}/> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};

// 校验装修页 banner信息是否完整
export const checkBanner = (customData: CustomValue): boolean => {
  for (const banner of customData.bannerList) {
    if (customData.isShowBanner && banner.isShowJump && !banner.bannerLink) {
      Message.error('请输入跳转链接');
      return false
    }
  }
  return true;
};

// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${prize.prizeName}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${prize.prizeName}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {

  for (const step of formData.seriesList) {
    if (step.prizeList.length > 0) {
      for (const prize of step.prizeList) {
        if (!prize.prizeName) {
          Message.error('请设置奖品');
          return false;
        }
        if (!isPrizeValid(prize, formData)) {
          return false;
        }
      }
    } else {
      Message.error('请设置奖品');
      return false;
    }
  }

  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }

  if (formData.skuList?.length <= 0) {
    Message.error('请上传商品与罐数数据');
    return false;
  }

  if (formData.seriesList?.length <= 0) {
    Message.error('请配置阶梯数据');
    return false;
  }

  if (!hasPrize(formData)) {
    return false;
  }

  const isHasSeriseShow = formData.seriesList?.findIndex(((item) => item.isShow))
  if (isHasSeriseShow === -1) {
    Message.error('至少显示一个阶梯数据');
    return false;
  }

  return true;
};
