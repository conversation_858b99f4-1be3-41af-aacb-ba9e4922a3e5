/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, {useReducer, useState} from 'react';
import {Form, Table, Input, Button, Dialog} from '@alifd/next';
import {formItemLayout, PageData, PRIZE_TYPE} from '../util';
import styles from './style.module.scss';
import {goToPropertyList, activityEditDisabled, isDisableSetPrize} from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({defaultValue, value, labelAlign = 'left'}: Props) => {
  const [formData] = useReducer((p: PageData, c: PageData) => {
    return {...p, ...c};
  }, value || defaultValue);
  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>

        <FormItem label="活动类型">{formData.type === 0 ? '集罐' : '满额'}</FormItem>
        <FormItem label="订单状态">已完成</FormItem>
        <FormItem label="订单奖励发放">订单确认收货{formData.days}天后发放奖励</FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderEndTime)}`}</FormItem>

        {formData.seriesList.map((item, index) => (
          <>
            <FormItem label="阶梯名称">{item.stepName}</FormItem>
            {formData.type === 0 ?
              (<FormItem label="阶梯罐数">{item.stepNum}罐</FormItem>)
              : (<FormItem label="阶梯金额">{item.stepNum}元</FormItem>)
            }

            <FormItem label="领取限制">
              {item.prizeReceiveLimit === 0 ? '单次领取' : '可重复领取'}
            </FormItem>
            {item.prizeReceiveLimit === 1 &&
              <FormItem label="可重复领取次数">{item.perReceiveCount}次</FormItem>
            }
            <FormItem label="奖品列表">
              <Table dataSource={item.prizeList} style={{marginTop: '15px'}}>
                <Table.Column title="奖品名称" dataIndex="prizeName"/>
                <Table.Column
                  title="奖品类型"
                  cell={(_, idx, row) => (
                    <div style={{cursor: 'pointer'}} onClick={() => goToPropertyList(row.prizeType)}>
                      {PRIZE_TYPE[row.prizeType]}
                    </div>
                  )}
                  dataIndex="prizeType"
                />
                {/*<Table.Column*/}
                {/*  title="单位数量"*/}
                {/*  cell={(_, idx, row) => {*/}
                {/*    if (row.prizeType === 1) {*/}
                {/*      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;*/}
                {/*    } else {*/}
                {/*      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;*/}
                {/*    }*/}
                {/*  }}*/}
                {/*/>*/}
                <Table.Column
                  title="发放份数"
                  cell={(_, idx, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="领取份数限制"
                  cell={(_, idx, row) => (<div>{row.maxReceiveCount}份</div>)
                  }
                />
                {/*<Table.Column*/}
                {/*  title="单份价值(元)"*/}
                {/*  cell={(_, idx, row) => (*/}
                {/*    <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>*/}
                {/*  )}*/}
                {/*/>*/}
                <Table.Column
                  title="奖品图"
                  cell={(_, idx, row) => <img style={{width: '30px'}} src={row.prizeImg} alt=""/>}
                />
              </Table>
            </FormItem>
          </>
        ))}

        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{width: '200px'}} alt=""/>
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{width: '200px'}} alt=""/>
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{width: '200px'}} alt=""/>
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules}/>
        </FormItem>
      </Form>
    </div>
  );
};
