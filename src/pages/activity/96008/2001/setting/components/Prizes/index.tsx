import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Input, Tab, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled, deepCopy, getParams } from '@/utils';
import { FormLayout, PRIZE_TYPE, PRIZE_INFO } from '../../../util';
import styles from '@/pages/yili/live/draw/setting/style.module.scss';
import dayjs from 'dayjs';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
export default ({ sRef, onChange, value, defaultValue }) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [skuVisible, setSkuVisible] = useState(false);
  const [sectionSkuList, setSectionSkuList] = useState([]);
  const [activeKey, setActiveKey] = useState('0');
  // 每个阶段是否刚刚添加了一个新奖品（按阶段索引管理）
  const [hasAddNewPrizeMap, setHasAddNewPrizeMap] = useState<Record<string, boolean>>({});
  // 当前操作类型
  const operationType: string = getParams('type');
  const isEdit: boolean = operationType === 'edit';
  const isDraft: boolean = operationType === 'draft';

  // 判断活动是否已开始
  const isActivityStarted = (): boolean => {
    if (!formData?.startTime) return false;
    const now = dayjs();
    const startTime = dayjs(formData.startTime);
    return now.isAfter(startTime);
  };

  // 同步/更新数据
  const setData = (data): void => {
    const newData = { ...formData, ...data };
    setFormData(newData);
    onChange(newData);
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 创建新的 sectionList 数组
    const newSectionList = formData.sectionList.map((section, index) => {
      if (index === parseInt(activeKey)) {
        const newPrizeList = [...section.sectionPrizeList];
        // 保留原有的 isNewlyAdded 标记（如果存在）
        const originalPrize = newPrizeList[target];
        newPrizeList[target] = {
          ...data,
          status: 1,
          // 如果原来的奖品有 isNewlyAdded 标记，则保留；否则不添加该字段
          ...(originalPrize?.isNewlyAdded && { isNewlyAdded: true }),
        };
        return {
          ...section,
          sectionPrizeList: newPrizeList,
        };
      }
      return section;
    });

    setData({ sectionList: newSectionList });
    setVisible(false);
    // 编辑完成后不要重置hasAddNewPrize状态，保持按钮置灰直到提交
  };

  /**
   * 上架新奖品，下架上一个启用奖品
   */
  const setNewPrizeInfo = (item) => {
    const num = item.sectionPrizeList.length;
    item.sectionPrizeList[num - 1].status = 0;
    // 为新奖品添加标记，表示这是新添加且未提交的奖品
    const newPrize = { ...PRIZE_INFO, isNewlyAdded: true };
    item.sectionPrizeList.push(newPrize);
    // 设置当前阶段已添加新奖品
    setHasAddNewPrizeMap((prev) => {
      const newMap = { ...prev, [activeKey]: true };
      return newMap;
    });
    setTarget(num);
    setVisible(true);
  };

  /**
   * 统一添加奖品到阶段的方法
   */
  const addPrizeToSection = (isNewlyAdded = false) => {
    // 创建新奖品对象
    const newPrize = {
      ...deepCopy(PRIZE_INFO),
      ...(isNewlyAdded && { isNewlyAdded: true }),
    };

    // 获取当前阶段的奖品列表长度，用于设置编辑目标
    const currentSection = formData.sectionList[parseInt(activeKey)];
    const currentLength = currentSection?.sectionPrizeList?.length || 0;

    // 更新 sectionList
    const newSectionList = formData.sectionList.map((section, index) => {
      if (index === parseInt(activeKey)) {
        const currentPrizeList = Array.isArray(section.sectionPrizeList) ? section.sectionPrizeList : [];
        return {
          ...section,
          sectionPrizeList: [...currentPrizeList, newPrize],
        };
      }
      return section;
    });

    setData({ sectionList: newSectionList });

    // 如果是新添加的奖品，设置状态
    if (isNewlyAdded) {
      setHasAddNewPrizeMap((prev) => {
        const newMap = { ...prev, [activeKey]: true };
        return newMap;
      });
    }

    // 设置编辑目标并打开弹窗
    setTarget(currentLength);
    setVisible(true);
  };

  // 只在组件初始化时重置一次，避免在数据更新时重复重置
  useEffect((): void => {
    setHasAddNewPrizeMap({});
  }, [operationType]); // 只依赖 operationType，不依赖 value

  // 单独处理 formData 的更新
  useEffect((): void => {
    setFormData(value);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => null; clearNewlyAddedFlags: () => void } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors: any): void => {
        console.log('errors', errors);
        err = errors;
      });
      return err;
    },
    // 提交成功后清除所有新添加奖品的标记
    clearNewlyAddedFlags: () => {
      const newSectionList = formData.sectionList.map((section) => ({
        ...section,
        sectionPrizeList: section.sectionPrizeList.map((prize) => {
          const { isNewlyAdded, ...prizeWithoutFlag } = prize;
          return prizeWithoutFlag;
        }),
      }));
      setData({ sectionList: newSectionList });
      // 提交成功后重置所有阶段的状态
      setHasAddNewPrizeMap({});
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="阶段商品及奖品设置">
        <Form {...formItemLayout} field={field}>
          <Tab
            activeKey={activeKey}
            onChange={(key) => setActiveKey(key)}
            unmountInactiveTabs
            triggerType={'click'}
            onClose={(key: any) => {
              Dialog.confirm({
                v2: true,
                title: '提示',
                centered: true,
                content: ' 删除该阶段项配置',
                onOk: () => {
                  const newSectionList = [...formData.sectionList];
                  newSectionList.splice(key, 1);
                  setActiveKey('0');
                  setData({ sectionList: newSectionList });
                },
                onCancel: () => {},
              } as any);
            }}
          >
            {formData.sectionList.map((item, TabIndex) => {
              return (
                <Tab.Item
                  closeable={activityEditDisabled() ? false : TabIndex > 0}
                  title={` 阶段项${TabIndex + 1}  `}
                  key={TabIndex}
                >
                  <Form {...formItemLayout} field={field}>
                    <FormItem required label="阶段" requiredMessage={'请输入阶段'}>
                      <Input
                        disabled={activityEditDisabled()}
                        value={item?.sectionType}
                        name={`sectionType-${TabIndex}`}
                        maxLength={10}
                        showLimitHint
                        className="w-300"
                        onChange={(sectionType) => {
                          const newSectionList = formData.sectionList.map((section, index) => {
                            if (index === TabIndex) {
                              return { ...section, sectionType };
                            }
                            return section;
                          });
                          setData({ sectionList: newSectionList });
                        }}
                      />
                    </FormItem>
                    <FormItem required label="阶段标题" requiredMessage={'请输入阶段标题'}>
                      <Input
                        value={item?.sectionName}
                        placeholder="请输入阶段标题"
                        name={`sectionName-${TabIndex}`}
                        maxLength={10}
                        showLimitHint
                        className="w-300"
                        onChange={(sectionName) => {
                          const newSectionList = formData.sectionList.map((section, index) => {
                            if (index === TabIndex) {
                              return { ...section, sectionName };
                            }
                            return section;
                          });
                          setData({ sectionList: newSectionList });
                        }}
                      />
                    </FormItem>
                    <FormItem label="所需罐数" requiredMessage={'请输入所需罐数'} required>
                      <NumberPicker
                        disabled={activityEditDisabled()}
                        value={item?.potNum}
                        onChange={(potNum: number) => {
                          formData.sectionList[TabIndex].potNum = potNum;
                          setData(formData);
                        }}
                        type="inline"
                        min={1}
                        max={9999999}
                        precision={0}
                        className={styles.number}
                      />
                      罐
                    </FormItem>
                    <FormItem label="转段后商品" name={`afterSkuList-${TabIndex}`}>
                      <Button
                        onClick={() => {
                          setSectionSkuList(item.afterSkuList || []);
                          setSkuVisible(true);
                        }}
                      >
                        查看SKU
                      </Button>
                    </FormItem>
                    <FormItem required label="奖品列表">
                      <FormItem>
                        <Table dataSource={item?.sectionPrizeList || []} style={{ marginTop: '15px' }}>
                          <Table.Column title="奖品名称" dataIndex="prizeName" />
                          <Table.Column
                            title="奖品图"
                            dataIndex="prizeImg"
                            cell={(_, index, row) => <img src={row.prizeImg} style={{ height: 50 }} alt="" />}
                          />
                          <Table.Column
                            title="奖品类型"
                            cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                            dataIndex="prizeType"
                          />
                          <Table.Column
                            title="单位数量"
                            cell={(_, index, row) => {
                              if (row.prizeType === 1) {
                                return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                              } else {
                                return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                              }
                            }}
                          />
                          <Table.Column
                            title="发放份数"
                            cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ''}</div>}
                          />
                          <Table.Column
                            title="单份价值(元)"
                            cell={(_, index, row) => (
                              <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                            )}
                          />
                          <Table.Column
                            title="每日发放限额"
                            cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
                          />
                          <Table.Column
                            title="奖品状态"
                            cell={(_, index, row) => (
                              <div>
                                {row && row.prizeName === '' ? '-' : row && row.status === 1 ? '已启用' : '下架'}
                              </div>
                            )}
                          />

                          <Table.Column
                            title="操作"
                            width={130}
                            cell={(val, index, _) => {
                              // 使用 isNewlyAdded 标记来判断是否是新添加且未提交的奖品
                              const isNewlyAdded = _.isNewlyAdded === true;
                              // 编辑按钮的禁用逻辑
                              let isEditDisabled = false;
                              if (isEdit) {
                                // 编辑模式：根据活动是否已开始决定
                                const activityStarted = isActivityStarted();
                                if (activityStarted) {
                                  // 已开始的活动：只有空奖品或者在默认数据中不存在的新奖品可以编辑
                                  const isEmpty = _.prizeName === '';

                                  // 检查这个奖品是否在默认数据中存在（即是否为原有奖品）
                                  const currentSectionIndex = parseInt(activeKey);
                                  const defaultPrizeList =
                                    defaultValue?.sectionList?.[currentSectionIndex]?.sectionPrizeList || [];
                                  const isExistingPrize = defaultPrizeList.some((defaultPrize, defaultIndex) => {
                                    return defaultIndex === index && defaultPrize.prizeName !== '';
                                  });

                                  // 只有空奖品或者不在默认数据中的奖品才可以编辑
                                  isEditDisabled = !isEmpty && isExistingPrize;
                                } else {
                                  // 未开始的活动：原有奖品可以编辑，新添加的奖品也可以编辑
                                  isEditDisabled = false;
                                }
                              }

                              // 删除按钮的显示和禁用逻辑
                              let showDeleteButton = false; // 默认不显示删除按钮
                              let isDeleteDisabled = false;
                              if (isEdit) {
                                // 编辑模式：根据活动是否已开始决定
                                const activityStarted = isActivityStarted();
                                if (activityStarted) {
                                  // 已开始的活动：只有真正新添加且未提交的奖品才显示删除按钮
                                  // 需要同时满足两个条件：
                                  // 1. 有 isNewlyAdded 标记
                                  // 2. 不在默认数据中存在（确实是新添加的）
                                  const currentSectionIndex = parseInt(activeKey);
                                  const defaultPrizeList =
                                    defaultValue?.sectionList?.[currentSectionIndex]?.sectionPrizeList || [];
                                  const isExistingInDefault = defaultPrizeList.some((defaultPrize, defaultIndex) => {
                                    return defaultIndex === index && defaultPrize.prizeName !== '';
                                  });

                                  // 只有标记为新添加且不在默认数据中的奖品才显示删除按钮
                                  showDeleteButton = isNewlyAdded === true && !isExistingInDefault;
                                  isDeleteDisabled = false; // 显示的删除按钮都是可用的
                                } else {
                                  // 未开始的活动：所有奖品都显示删除按钮且可用
                                  showDeleteButton = true;
                                  isDeleteDisabled = false;
                                }
                              } else {
                                // 其他模式：显示删除按钮且可用
                                showDeleteButton = true;
                                isDeleteDisabled = false;
                              }

                              return (
                                <FormItem>
                                  <Button
                                    text
                                    type="primary"
                                    disabled={isEditDisabled}
                                    onClick={() => {
                                      let row = item.sectionPrizeList[index];
                                      if (row.prizeName === '') {
                                        row = null;
                                      }
                                      setEditValue(row);
                                      setTarget(index);
                                      setVisible(true);
                                    }}
                                  >
                                    <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                                  </Button>
                                  {showDeleteButton && (
                                    <Button
                                      text
                                      type="primary"
                                      disabled={isDeleteDisabled}
                                      onClick={() => {
                                        // 对于有奖品内容的行，显示确认对话框
                                        if (_.prizeType || _.prizeName) {
                                          Dialog.confirm({
                                            v2: true,
                                            title: '提示',
                                            centered: true,
                                            content: '确认删除该奖品？',
                                            onOk: () => {
                                              // 如果是新添加且未提交的奖品，需要特殊处理
                                              if (isNewlyAdded) {
                                                // 重置当前阶段的添加状态
                                                setHasAddNewPrizeMap((prev) => ({ ...prev, [activeKey]: false }));

                                                // 如果删除的是最后一个奖品且前面有被下架的奖品，重新启用前一个奖品
                                                const currentPrizeList =
                                                  formData.sectionList[TabIndex].sectionPrizeList;
                                                if (index === currentPrizeList.length - 1 && index > 0) {
                                                  const previousPrize = currentPrizeList[index - 1];
                                                  if (previousPrize.status === 0) {
                                                    // 重新启用上一个奖品
                                                    formData.sectionList[TabIndex].sectionPrizeList[
                                                      index - 1
                                                    ].status = 1;
                                                  }
                                                }
                                              }

                                              // 删除奖品行
                                              formData.sectionList[TabIndex].sectionPrizeList.splice(index, 1);
                                              setData(formData);
                                            },
                                            onCancel: () => console.log('cancel'),
                                          } as any);
                                        } else {
                                          // 对于空白行（新添加的未配置奖品），直接删除无需确认
                                          // 如果是新添加且未提交的奖品，需要特殊处理
                                          if (isNewlyAdded) {
                                            // 重置当前阶段的添加状态
                                            setHasAddNewPrizeMap((prev) => ({ ...prev, [activeKey]: false }));

                                            // 如果删除的是最后一个奖品且前面有被下架的奖品，重新启用前一个奖品
                                            const currentPrizeList = formData.sectionList[TabIndex].sectionPrizeList;
                                            if (index === currentPrizeList.length - 1 && index > 0) {
                                              const previousPrize = currentPrizeList[index - 1];
                                              if (previousPrize.status === 0) {
                                                // 重新启用上一个奖品
                                                formData.sectionList[TabIndex].sectionPrizeList[index - 1].status = 1;
                                              }
                                            }
                                          }
                                          // 删除空白奖品行
                                          formData.sectionList[TabIndex].sectionPrizeList.splice(index, 1);
                                          setData(formData);
                                        }
                                      }}
                                    >
                                      <i className={`iconfont icon-shanchu`} />
                                    </Button>
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                        </Table>
                      </FormItem>
                      <FormItem>
                        {(() => {
                          // 编辑模式
                          if (activityEditDisabled()) {
                            const activityStarted = isActivityStarted();
                            // 未开始的活动：不显示按钮
                            if (!activityStarted) return null;
                            // 已开始的活动：显示添加新奖品按钮
                            return (
                              <Button
                                disabled={
                                  formData.sectionList[activeKey].sectionPrizeList.length >
                                  defaultValue.sectionList[activeKey].sectionPrizeList.length
                                }
                                type="secondary"
                                style={{ marginTop: 10 }}
                                onClick={() => {
                                  setNewPrizeInfo(item);
                                }}
                              >
                                添加新奖品并下架上一个启用奖品
                              </Button>
                            );
                          }
                          // 创建新活动模式
                          const hasAnyPrize = item?.sectionPrizeList?.length > 0;
                          return (
                            <Button
                              disabled={hasAnyPrize}
                              type="secondary"
                              style={{ marginTop: 10 }}
                              onClick={() => {
                                addPrizeToSection(false);
                              }}
                            >
                              添加奖品
                            </Button>
                          );
                        })()}
                      </FormItem>
                    </FormItem>
                  </Form>
                </Tab.Item>
              );
            })}
          </Tab>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          defaultEditValue={defaultValue?.sectionList?.[activeKey]?.sectionPrizeList?.[target] ?? null}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit
          // typeList={[1, 2, 3, 4, 6, 7, 8]}
          typeList={[1, 2, 3, 8]}
          defaultTarget={2}
        />
      </LzDialog>
      {/*  sku列表 */}

      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {sectionSkuList && sectionSkuList.length > 0 && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={sectionSkuList} fixedHeader>
              <Table.Column title="阶段" dataIndex="sectionType" />
              <Table.Column title="罐数" dataIndex="skuPotNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
