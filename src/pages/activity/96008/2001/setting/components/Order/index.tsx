import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Field, Form, Radio, Select, NumberPicker, DatePicker2, Message, Button, Upload, Table } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, downloadExcel, getShopOrderStartTime, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import { skuTemplateBeforeExport } from '@/api/v96008';
import { APP_MODE, config } from 'ice';
import CONST from '@/utils/constant';
import LzDialog from '@/components/LzDialog';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [skuVisible, setSkuVisible] = useState(false);
  const [beforeSkuList, setBeforeSkuList] = useState([]);
  const env = APP_MODE;
  console.log('当前环境：NODE_ENV：', process.env.NODE_ENV, '，APP_MODE：', APP_MODE);
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 下载前置sku模板
   */
  const downloadTemplate = async () => {
    try {
      const data: any = await skuTemplateBeforeExport();
      downloadExcel(data, '前置sku导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
      setTimeout(() => {
        field.validate('orderBeforeRestrainRangeData');
      }, 1000);
    });
  }, [formData.endTime]);

  const [maxDays, setMaxDays] = React.useState(180);
  const [firstIn, setFirstIn] = React.useState(false);
  useEffect(() => {
    if (!firstIn) {
      setFirstIn(true);
      return;
    }
    let diff = 180;
    if (shopOrderInfo.longTermOrder) {
      diff = dayjs(formData.endTime).diff(dayjs(shopOrderInfo.shopOrderStartTime), 'day');
    } else {
      diff = shopOrderInfo.orderRetentionDays;
    }
    setMaxDays(diff);
    if (diff < formData.days) {
      setData({ days: diff });
    }
  }, [formData.startTime, shopOrderInfo.shopOrderStartTime]);
  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };
  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    const orderStartTime = val[0];
    const orderEndTime = val[1];
    if (!orderStartTime || !orderEndTime) {
      callback('请选下单时间');
    } else if (!shopOrderInfo.longTermOrder && dayjs(orderStartTime).valueOf() < shopOrderInfo.shopOrderStartTime) {
      callback(`下单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else {
      callback();
    }
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRangeData): void => {
    setData({
      orderRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRangeData[1]),
    });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field}>
          <FormItem
            label="前置订单时间"
            required
            requiredMessage="请设置前置订单时间范围"
            disabled={activityEditDisabled()}
          >
            近{' '}
            <NumberPicker
              precision={2}
              min={0}
              max={maxDays}
              disabled
              type="inline"
              value={formData.days}
              onChange={(days: number) => setData({ days })}
            />{' '}
            天已购
          </FormItem>
          <FormItem label="前置SKU上传">
            <Message type="notice" style={{ marginBottom: 10 }}>
              导入须知： <br />
              1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
              <br />
              2.单次导入最大5M，导入中请不要关闭此页面。
              <br />
              <Button text type="primary" onClick={downloadTemplate} disabled={activityEditDisabled()}>
                下载模板
              </Button>
            </Message>

            <Upload
              disabled={activityEditDisabled()}
              action={`${config.baseURL}/96008/importSkuExcelBefore`}
              name="file"
              method="post"
              headers={{
                token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                prd: localStorage.getItem(CONST.LZ_SSO_PRD),
              }}
              // ref={saveUploaderRef}
              value={formData.skuFileList}
              fileNameRender={(file) => <span>{formData.skuFileName}</span>}
              limit={1}
              listType="text"
              accept=".xls,.xlsx"
              onChange={(info) => {
                if (info.length) {
                  if (info[0].size > 5 * 1024 * 1024) {
                    Message.error('文件大小不能超过5M');
                    return;
                  }
                }
                console.log(info[0].name, 'info[0].name');
                // prizesRef.splice(0);
                setData({ skuFileName: info[0].name, skuFileList: info });
              }}
              onError={(res) => {
                if (res.state === 'error') {
                  if (res.response?.message) {
                    Message.error(res.response?.message);
                  } else {
                    Message.error('文件错误，请上传正确的文件');
                  }
                }
              }}
              onRemove={(file) => {
                setData({ skuFileList: [], beforeSkuList: [] });
              }}
              onSuccess={(res) => {
                if (res.response.code === 200) {
                  setData({ beforeSkuList: res.response.data });
                } else if (res.response?.message) {
                  setData({ skuFileList: [] });
                  Message.error(res.response?.message);
                } else {
                  setData({ skuFileList: [] });
                  Message.error('文件错误，请上传正确的文件');
                }
              }}
              style={{ marginBottom: 10 }}
            >
              <div className="next-upload-drag">
                <p className="next-upload-drag-icon">
                  <Button type="primary" disabled={activityEditDisabled()}>
                    上传前置sku数据
                  </Button>
                </p>
                <p className="next-upload-drag-hint">支持xls类型的文件</p>
              </div>
            </Upload>
            {formData.beforeSkuList.length > 0 && (
              <Button
                onClick={() => {
                  setBeforeSkuList(formData.beforeSkuList || []);
                  setSkuVisible(true);
                }}
              >
                查看SKU
              </Button>
            )}
          </FormItem>
          <FormItem
            label="后置订单下单时间"
            required
            requiredMessage="请选后置订单下单时间"
            validator={validateOrderTime}
            disabled={activityEditDisabled()}
          >
            <RangePicker
              className="w-300"
              name="orderRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.orderRangeData || [new Date(formData.orderStartTime), new Date(formData.orderEndTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.startOf('day').valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).valueOf();
              }}
            />
            {/* <div className={styles.tip}> */}
            {/*  注：1、默认支持查询 */}
            {/*  {shopOrderInfo.longTermOrder */}
            {/*    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后` */}
            {/*    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`} */}
            {/*  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。 */}
            {/*  <br /> */}
            {/*  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。 */}
            {/* </div> */}
          </FormItem>
          <FormItem label="订单状态" disabled={activityEditDisabled()}>
            <Select
              dataSource={ORDER_STATUS}
              value={formData.orderRestrainStatus}
              onChange={handleStatusChange}
              disabled
            />
          </FormItem>
          <FormItem label="奖品延迟发放" required disabled={activityEditDisabled()}>
            {(env === 'test' || env === 'dev') && (
              <FormItem disabled={activityEditDisabled()}>
                <Radio.Group
                  value={formData.isDelayedDisttribution}
                  onChange={(isDelayedDisttribution) =>
                    setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution })
                  }
                >
                  <Radio value={0}>否</Radio>
                  <Radio value={1}>是</Radio>
                </Radio.Group>
              </FormItem>
            )}
            {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && (
              <FormItem required requiredMessage="请输入延迟天数" disabled={activityEditDisabled() || env === 'prod'}>
                延迟发放{' '}
                <NumberPicker
                  min={1}
                  max={7}
                  type="inline"
                  value={formData.awardDays}
                  onChange={(awardDays: number) => setData({ awardDays })}
                />{' '}
                天
              </FormItem>
            )}
          </FormItem>
        </Form>
      </LzPanel>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {beforeSkuList && beforeSkuList.length > 0 && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={beforeSkuList} fixedHeader>
              <Table.Column title="阶段" dataIndex="sectionType" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
