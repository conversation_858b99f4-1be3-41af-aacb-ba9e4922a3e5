/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useReducer, useRef, useState } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 抽奖机会
// import EndActivity from './components/EndActivity';
// 奖品信息
import PrizesInfo from './components/Prizes';
// 分享设置
// 活动规则
import RulesInfo from './components/Rules';
// 参与人群
import Crowd from './components/Crowd';
// 订单限制
import Order from './components/Order';
// 曝光商品
import Exposure from './components/Exposure';
import { CustomValue, PageData } from '@/pages/activity/90090/1001/util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{
    submit: () => void;
  }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [renderBase] = useState(true);
  const [renderPrize, setRenderPrize] = useState(formData.threshold !== 1);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    if (activityInfo.supportLevels) {
      setTimeout(() => {
        setRenderPrize(true);
      });
    }
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const crowdRef = useRef<{ submit: () => void | null }>(null);
  const orderRef = useRef<{ submit: () => void | null }>(null);
  // const endActivityRef = useRef<{ submit: () => void | null }>(null);
  const exposureRef = useRef<{ submit: () => void | null }>(null);
  // const shareRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);
  const prizesRef = useRef<{ submit: () => void | null }>(null);

  // 检验除规则外表单
  const checkForm = (): boolean => {
    const events: any[] = [
      baseRef.current!,
      crowdRef.current,
      orderRef.current,
      // endActivityRef.current,
      exposureRef.current,
      // shareRef.current,
      prizesRef.current,
    ];
    return events.every((item, index: number): boolean => {
      const result = events[index].submit();
      if (result) {
        return false;
      }
      return true;
    });
  };

  // 传递Ref
  useImperativeHandle(
    sRef,
    (): {
      submit: () => void;
    } => ({
      submit: (): void => {
        // 异常结果列表
        const resultList: unknown[] = [];
        // 收集所有Form的submit 验证方法
        const events: any[] = [
          baseRef.current!,
          // crowdRef.current,
          orderRef.current,
          // endActivityRef.current,
          exposureRef.current,
          // shareRef.current,
          prizesRef.current,
          rulesRef.current,
        ];
        // 批量执行submit方法
        // submit只会抛出异常，未有异常返回null
        events.every((item, index: number): boolean => {
          const result = events[index].submit();
          console.log(result);
          if (result) {
            resultList.push(result);
            return false;
          }
          return true;
        });
        onSubmit(resultList.length);
      },
    }),
  );
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      <Crowd sRef={crowdRef} {...settingProps} />
      <Order sRef={orderRef} {...settingProps} />
      <PrizesInfo sRef={prizesRef} {...settingProps} />
      {/* <EndActivity sRef={endActivityRef} {...settingProps} /> */}
      <Exposure sRef={exposureRef} {...settingProps} />
      {/* <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} /> */}
      <RulesInfo sRef={rulesRef} {...settingProps} checkForm={checkForm} />
    </div>
  );
};
