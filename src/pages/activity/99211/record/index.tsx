import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from './components/WinRecord';
import DataRecord from './components/DataRecord';
import UserParticipationData from './components/UserParticipationData';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('0');
  return (
    <div className="crm-container">
      <LzPanel title="骁龙能量站数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="活动数据" key="0">
            <DataRecord />
          </Tab.Item>
          <Tab.Item title="用户参与数据" key="2">
            <UserParticipationData />
          </Tab.Item>
          <Tab.Item title="发放记录" key="1">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
