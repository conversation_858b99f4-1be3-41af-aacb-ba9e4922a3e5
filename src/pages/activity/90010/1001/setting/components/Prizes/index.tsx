/**
 * Author: liangyu
 * Date: 2024-01-05 14:58
 * Description:
 */
import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { Button, Dialog, Field, Form, Input, NumberPicker, Radio, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy, getParams, isDisableSetPrize, numRegularCheckInt } from '@/utils';
import {
  FormLayout,
  GIVEAWAY_INFO,
  GRADE_LABEL,
  PageData,
  PRIZE_INFO,
  PRIZE_TYPE,
  TaskRequestInfo,
} from '../../../util';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import ChoosePromotion from '@/components/ChoosePromotion';
import { details } from '@/api/formalSku';
import UploadMemberId from '../UploadMemberId';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

let cpbSkuList = [];

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // const [loading, setLoading] = useState(false);

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑赠品行index
  const [giveaTarget, setGiveaTarget] = useState(0);
  // 已经选择的奖品
  const [plan, setPlan] = useState<any[]>([]);
  // 同步/更新数据
  const setData = (data): void => {
    // console.log(data, '同步/更新数据1111');
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const getCpbSkuList = async () => {
    try {
      const data = await details();
      cpbSkuList = data;
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  useEffect((): void => {
    if (formData.taskRequestList.length === 0) {
      // 生成默认奖品列表
      const taskRequestList: TaskRequestInfo[] = [...formData.taskRequestList];
      // 补齐奖品数到5
      for (let i = 0; i < 4; i++) {
        GIVEAWAY_INFO.prizeList[0] = deepCopy(PRIZE_INFO);
        taskRequestList[i] = deepCopy(GIVEAWAY_INFO);
        taskRequestList[i].level = GRADE_LABEL[i].gradeLevel;
      }
      formData.taskRequestList = taskRequestList.length ? taskRequestList : formData.taskRequestList;
      setData({ taskRequestList: taskRequestList.length ? taskRequestList : formData.taskRequestList });
    }
    getCpbSkuList().then((data) => {
      if (formData.taskRequestList[0].cpbJoinSkuType === 1) {
        formData.taskRequestList[0].skuList = data;
      }
      if (formData.taskRequestList[1].cpbJoinSkuType === 1) {
        formData.taskRequestList[1].skuList = data;
      }
      setData(formData);
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };
  /**
   * 选择令牌
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data, index1): boolean | void => {
    // console.log(data, '选择令牌data==========');
    if (data) {
      data.sendTotalCount = formData.taskRequestList[index1].sendTotalCount1;
      // 更新指定index 奖品信息
      formData.taskRequestList[index1].prizeList[0] = data;
    } else {
      formData.taskRequestList[index1].prizeList[0] = PRIZE_INFO;
    }
    // 需要将所有会员等级下的prizeList合并到一个数组总，为了做选择奖品时候的去重校验
    let list1 = [];
    formData.taskRequestList.forEach((item: any, index: number) => {
      list1 = list1.concat(item.prizeList[0]);
    });
    const listNew = list1.map((item: any) => item.prizeKey);
    setPlan(listNew);
    setData(formData);
    setVisible(false);
  };
  // 选择除令牌外的奖品
  const onPrizeChangeOther = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.taskRequestList[giveaTarget].prizeList[0] = data;
    // 需要将所有会员等级下的prizeList合并到一个数组总，为了做选择奖品时候的去重校验
    let list1 = [];
    formData.taskRequestList.forEach((item: any, index: number) => {
      list1 = list1.concat(item.prizeList[0]);
    });
    const listNew = list1.map((item: any) => item.prizeKey);
    setPlan(listNew);
    setData(formData);
    setVisible(false);
  };
  // 选择商品
  const handleSkuChange = (data) => {
    // console.log(giveaTarget, data, '选择商品giveaTarget');
    formData.taskRequestList[giveaTarget].skuList = data;
    setData(formData);
  };

  // 会员等级奖品规则
  const gradeRuleChange = (items, index1, rules) => {
    formData.taskRequestList[index1].taskRule = rules;
    setData(formData);
  };
  const gradeSkuIdChange = (items, index1, skuId) => {
    // console.log(skuId, 'skuId==========');
    const regex = /^[0-9]*$/;
    if (regex.test(skuId)) {
      formData.taskRequestList[index1].giftSkuList[0] = skuId;
    }
    setData(formData);
  };
  // 令牌发放份数
  const linPaiOnchange = (sendTotalCount, index1) => {
    formData.taskRequestList[index1].sendTotalCount1 = sendTotalCount;
    formData.taskRequestList[index1].prizeList[0].sendTotalCount = sendTotalCount;
    setData(formData);
  };
  const radioGroupChange = (cpbJoinSkuType, items, index1) => {
    formData.taskRequestList[index1].cpbJoinSkuType = cpbJoinSkuType;
    if (items.cpbJoinSkuType === 2) {
      formData.taskRequestList[index1].skuList = [];
    } else {
      formData.taskRequestList[index1].skuList = deepCopy(cpbSkuList);
    }
    setData(formData);
  };
  // 奖品类型选择
  const radioPrizeGroupChange = (prizeType, items, index1) => {
    console.log(prizeType, items, '奖品类型选择======');
    formData.taskRequestList[index1].prizeType = prizeType;
    items = deepCopy(PRIZE_INFO);
    formData.taskRequestList[index1].prizeList[0] = items;
    if (prizeType === 3) {
      formData.taskRequestList[index1].giftSkuList = [];
    }
    setPlan([]);
    setData(formData);
  };
  return (
    <div>
      <LzPanel
        title="会员奖品设置"
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.location.href = 'https://bigitem.oss-cn-zhangjiakou.aliyuncs.com/cpb/memberId.xlsx';
            }}
          >
            下载memberId模板
          </Button>
        }
      >
        {/* eslint-disable-next-line complexity */}
        {formData.taskRequestList.map((items, index1) => {
          return (
            <div key={index1}>
              <Form {...formItemLayout} field={field} key={index1}>
                <FormItem disabled={activityEditDisabled()} label="" required className={styles.awardTitle}>
                  {GRADE_LABEL[index1].gradeName}会员
                </FormItem>
                <FormItem label="奖品类型">
                  <Radio.Group
                    value={items.prizeType}
                    disabled={activityEditDisabled()}
                    onChange={(prizeType: number) => {
                      radioPrizeGroupChange(prizeType, items, index1);
                    }}
                  >
                    <Radio id="1" value={11}>
                      单品促销令牌
                    </Radio>
                    <Radio id="2" value={3}>
                      实物
                    </Radio>
                  </Radio.Group>
                </FormItem>
                {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                  <FormItem label="参与活动正装商品" required requiredMessage="请选择参与活动正装商品" disabled={false}>
                    <Radio.Group
                      value={items.cpbJoinSkuType}
                      disabled={activityEditDisabled()}
                      onChange={(cpbJoinSkuType: number) => {
                        radioGroupChange(cpbJoinSkuType, items, index1);
                      }}
                    >
                      <Radio id="1" value={1}>
                        全店正装商品
                      </Radio>
                      <Radio id="2" value={2}>
                        指定商品
                      </Radio>
                      {/* <Radio id="3" value={3}> */}
                      {/*  排除商品 */}
                      {/* </Radio> */}
                    </Radio.Group>
                    {items.cpbJoinSkuType === 2 && (
                      <div
                        onClick={() => {
                          setGiveaTarget(index1);
                        }}
                      >
                        <ChooseGoods
                          disabled={activityEditDisabled()}
                          value={items.skuList}
                          onChange={handleSkuChange}
                        />
                      </div>
                    )}
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <FormItem disabled={activityEditDisabled()} label="添加令牌" required>
                    <ChoosePromotion
                      promoType={'1'}
                      submit={(val) => {
                        onPrizeChange(val, index1);
                      }}
                      index={index1}
                      planList={plan}
                      value={
                        formData.taskRequestList[index1].prizeList &&
                        formData.taskRequestList[index1].prizeList[0] &&
                        formData.taskRequestList[index1].prizeList[0].prizeKey
                          ? formData.taskRequestList[index1].prizeList[0]
                          : null
                      }
                      disabled={activityEditDisabled() || getParams('type') === 'edit'}
                    />
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <FormItem
                    disabled={activityEditDisabled()}
                    label="令牌发放份数"
                    required
                    requiredMessage="请输入令牌发放份数"
                    validator={numRegularCheckInt}
                  >
                    <NumberPicker
                      min={1}
                      type="inline"
                      name={`sendTotalCount${index1}`}
                      value={items.prizeList[0].sendTotalCount}
                      onChange={(sendTotalCount1: number) => {
                        linPaiOnchange(sendTotalCount1, index1);
                      }}
                      className={styles.number}
                    />
                  </FormItem>
                )}
                {items.prizeType === 3 && (
                  <FormItem disabled={activityEditDisabled()} label="添加实物" required>
                    <Table dataSource={items.prizeList} style={{ marginTop: '15px' }}>
                      <Table.Column
                        title="位置"
                        dataIndex="prizeName"
                        width={50}
                        cell={(_, index, row) => <div>{index + 1}</div>}
                      />
                      <Table.Column title="奖品名称" dataIndex="prizeName" />
                      <Table.Column
                        title="奖品类型"
                        cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                        dataIndex="prizeType"
                      />
                      <Table.Column
                        title="单位数量"
                        cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                      />
                      <Table.Column
                        title="发放份数"
                        cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                      />
                      <Table.Column
                        title="单份价值(元)"
                        cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                      />
                      <Table.Column
                        title="奖品图"
                        cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                      />
                      {!activityEditDisabled() && (
                        <Table.Column
                          title="操作"
                          width={80}
                          cell={(val, index, _) => (
                            <FormItem disabled={isDisableSetPrize(items.prizeList, index)}>
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  let row = items.prizeList[index];
                                  if (row.prizeType === 0) {
                                    row = null;
                                  }
                                  setEditValue(row);
                                  setGiveaTarget(index1);
                                  setVisible(true);
                                }}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  if (_.prizeType) {
                                    Dialog.confirm({
                                      v2: true,
                                      title: '提示',
                                      centered: true,
                                      content: '确认清空该奖品？',
                                      onOk: () => {
                                        items.prizeList.splice(index, 1, PRIZE_INFO);
                                        setData(formData);
                                      },
                                      onCancel: () => console.log('cancel'),
                                    } as any);
                                  }
                                }}
                              >
                                <i className={`iconfont icon-shanchu`} />
                              </Button>
                            </FormItem>
                          )}
                        />
                      )}
                    </Table>
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <FormItem
                    disabled={activityEditDisabled()}
                    label="SKU ID"
                    required
                    requiredMessage="请输入令牌所绑定的单品促销活动的正装商品SKU ID，用来点击申领后跳转"
                  >
                    <Input
                      placeholder="请输入令牌所绑定的单品促销活动的正装商品SKU ID，用来点击申领后跳转"
                      value={items.giftSkuList[0]}
                      name={`skuId${index1}`}
                      className="w-300"
                      onChange={(skuId) => gradeSkuIdChange(items, index1, skuId)}
                    />
                    <p className={styles.tip}>
                      1.输入令牌所绑定的单品促销活动的正装商品SKU ID，用于用户点击“即刻申请”领取令牌后，跳转对应的商详页
                    </p>
                    <p className={styles.tip}>
                      2.请务必填写当前会员等级对应的SKU ID，填写错将导致链接跳转错误，产生客诉
                    </p>
                  </FormItem>
                )}
                {items.prizeType === 3 && (
                  <FormItem label="发放时机">
                    {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                      <p className={styles.tip}>
                        用户符合升级礼参与门槛，并且活动期间内在店铺成功下单付款正装产品订单且确认收货后，前往活动页面点击“即刻申请”按钮后，发放实物
                      </p>
                    )}
                    {(GRADE_LABEL[index1].gradeLevel === '3' ||
                      GRADE_LABEL[index1].gradeLevel === '4' ||
                      GRADE_LABEL[index1].gradeLevel === '5') && (
                      <p className={styles.tip}>用户符合升级礼参与门槛，前往活动页面点击“即刻申请”按钮后，发放实物</p>
                    )}
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <FormItem label="发放时机">
                    {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                      <p className={styles.tip}>
                        用户符合升级礼参与门槛，并且活动期间内在店铺成功下单付款正装产品订单且确认收货后，前往活动页面点击“即刻申请”按钮后，发放令牌
                      </p>
                    )}
                    {(GRADE_LABEL[index1].gradeLevel === '3' ||
                      GRADE_LABEL[index1].gradeLevel === '4' ||
                      GRADE_LABEL[index1].gradeLevel === '5') && (
                      <p className={styles.tip}>用户符合升级礼参与门槛，前往活动页面点击“即刻申请”按钮后，发放令牌</p>
                    )}
                  </FormItem>
                )}

                <FormItem label="member ID" required requiredMessage="请上传memberID">
                  <UploadMemberId
                    activityKey={formData.taskRequestList[index1].activityKey}
                    onChange={(activityKey) => {
                      formData.taskRequestList[index1].activityKey = activityKey;
                      setData(formData);
                      if (activityKey) {
                        field.setError(`activityKey${index1}`, '');
                      } else {
                        field.setError(`activityKey${index1}`, '请上传memberID');
                      }
                    }}
                  />
                  <Input
                    className="validateInput"
                    name={`activityKey${index1}`}
                    value={formData.taskRequestList[index1].activityKey}
                  />
                </FormItem>

                <FormItem label="活动规则" required requiredMessage="请输入活动规则">
                  <Input.TextArea
                    value={items.taskRule}
                    name={`rules${index1}`}
                    onChange={(rules) => gradeRuleChange(items, index1, rules)}
                    // autoHeight={{ minRows: 8, maxRows: 40 }}
                    placeholder="请输入活动规则说明"
                    maxLength={5000}
                    showLimitHint
                    className="form-input-ctrl"
                  />
                </FormItem>
              </Form>
            </div>
          );
        })}
      </LzPanel>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          hasProbability={false}
          hasLimit={false}
          editValue={editValue}
          onChange={onPrizeChangeOther}
          onCancel={onCancel}
          typeList={[3]}
          defaultTarget={3}
        />
      </LzDialog>
    </div>
  );
};
