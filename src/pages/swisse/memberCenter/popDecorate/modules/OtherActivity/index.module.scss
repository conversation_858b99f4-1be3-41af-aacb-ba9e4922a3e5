$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;

  .member-rule {
    width: 100%;
    object-fit: contain;
    //position: absolute;
    //top: 0;
  }

  .other-div {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    box-sizing: border-box;
    padding-top: 3px*$px-scale;
    padding-left: 1px*$px-scale;
    padding-right: 1px*$px-scale;
  }

  .activity-img {
    width: 100%;
    //margin-top: 0.25px*$px-scale;
  }

  .activity-title {
    width: 100%;
    height: 1.6px*$px-scale;
    overflow: hidden;
    display: flex;
    justify-content: space-around;

    .title {
      width: 3.5px*$px-scale;
      height: 1.6px*$px-scale;
      margin-right: 0.125px*$px-scale;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/238779/31/19280/541/670f5f38F1835a7a9/3055ff37c3354c84.png);
      background-size: contain;
      background-repeat: no-repeat;
      background-position: bottom;
      text-align: center;
      color: #fff;
      font-size: 0.625px*$px-scale;
      line-height: 1.125px*$px-scale;
      box-sizing: border-box;
      padding-top: 0.5px*$px-scale;
    }

    .title-select {
      width: 3.5px*$px-scale;
      height: 1.6px*$px-scale;
      margin-right: 0.1px*$px-scale;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/222535/25/44803/1065/670f5f14Fca433510/a5382eb194143136.png);
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: bottom;
      text-align: center;
      color: #c82d31;
      font-size: 0.625px*$px-scale;
      line-height: 1.125px*$px-scale;
      box-sizing: border-box;
      padding-top: 0.5px*$px-scale;
    }
  }
}

.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;

    .imgUpload {
      display: flex;
      align-items: center;

      .tip {
        margin-left: 10px;
      }
    }

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
}
