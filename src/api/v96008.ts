import {
  Activity96008CreateOrUpdateRequest,
  Activity96008CreateOrUpdateResponse,
  Activity96008DetailDataResp,
  Activity96008ReportRequest,
  Activity96008SectionInfoResponse,
  Activity96008SkuImportBeforeResponse,
  Activity96008SkuTotalResponse,
  Activity96008UserPrizeRecordPageRequest,
  Activity96008UserWinningLogRequest,
  IPageActivity96008UserPotRecordPageResponse,
  IPageActivity96008UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 雀巢转段礼
 * @summary 创建活动
 * @request POST:/96008/createActivity
 */
export const createActivity = (
  request: Activity96008CreateOrUpdateRequest,
): Promise<Activity96008CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96008/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 获取本活动的段数信息
 * @request POST:/96008/data/getSectionInfo
 */
export const dataGetSectionInfo = (
  request: Activity96008UserPrizeRecordPageRequest,
): Promise<Activity96008SectionInfoResponse[]> => {
  return httpRequest({
    url: '/96008/data/getSectionInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 数据报表
 * @request POST:/96008/data/report
 */
export const dataReport = (request: Activity96008ReportRequest): Promise<Activity96008DetailDataResp> => {
  return httpRequest({
    url: '/96008/data/report',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 数据报表导出
 * @request POST:/96008/data/report/export
 */
export const dataReportExport = (request: Activity96008ReportRequest): Promise<void> => {
  return httpRequest({
    url: '/96008/data/report/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 用户罐数记录列表
 * @request POST:/96008/data/userPotRecordPage
 */
export const dataUserPotRecordPage = (
  request: Activity96008UserPrizeRecordPageRequest,
): Promise<IPageActivity96008UserPotRecordPageResponse> => {
  return httpRequest({
    url: '/96008/data/userPotRecordPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 用户罐数记录列表导出
 * @request POST:/96008/data/userPotRecordPage/export
 */
export const dataUserPotRecordPageExport = (request: Activity96008UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/96008/data/userPotRecordPage/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 上传领奖人群包
 * @request POST:/96008/data/winning/uploadWinningPin
 */
export const dataWinningUploadWinningPin = (
  activity96008ActivityDataRequest: Activity96008UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/96008/data/winning/uploadWinningPin',
    method: 'post',
    data: activity96008ActivityDataRequest,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 奖品领取记录
 * @request POST:/96008/data/winningLog
 */
export const dataWinningLog = (
  activity96008UserWinningLogRequest: Activity96008UserWinningLogRequest,
): Promise<IPageActivity96008UserWinningLogResponse> => {
  return httpRequest({
    url: '/96008/data/winningLog',
    method: 'post',
    data: activity96008UserWinningLogRequest,
  });
};

/**
 * @tags 雀巢转段礼记录
 * @summary 奖品领取记录导出
 * @request POST:/96008/data/winningLog/export
 */
export const dataWinningLogExport = (
  activity96008UserWinningLogRequest: Activity96008UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/96008/data/winningLog/export',
    method: 'post',
    data: activity96008UserWinningLogRequest,
  });
};

/**
 * @tags 雀巢转段礼
 * @summary 导入sku信息excel
 * @request POST:/96008/importSkuExcel
 */
export const importSkuExcel = (file: any): Promise<Activity96008SkuTotalResponse[]> => {
  return httpRequest({
    url: '/96008/importSkuExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 雀巢转段礼
 * @summary 导入前置sku信息excel
 * @request POST:/96008/importSkuExcelBefore
 */
export const importSkuExcelBefore = (file: any): Promise<Activity96008SkuImportBeforeResponse[]> => {
  return httpRequest({
    url: '/96008/importSkuExcelBefore',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 雀巢转段礼
 * @summary 下载sku模板
 * @request POST:/96008/skuTemplate/export
 */
export const skuTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/96008/skuTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 雀巢转段礼
 * @summary 下载前置sku模板
 * @request POST:/96008/skuTemplateBefore/export
 */
export const skuTemplateBeforeExport = (): Promise<void> => {
  return httpRequest({
    url: '/96008/skuTemplateBefore/export',
    method: 'post',
  });
};

/**
 * @tags 雀巢转段礼
 * @summary 修改活动
 * @request POST:/96008/updateActivity
 */
export const updateActivity = (
  request: Activity96008CreateOrUpdateRequest,
): Promise<Activity96008CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96008/updateActivity',
    method: 'post',
    data: request,
  });
};
