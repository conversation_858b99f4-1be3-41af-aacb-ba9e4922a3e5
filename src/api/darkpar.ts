import { request as httpRequest } from 'ice';

/**
 * @tags dkp-common-query-test-controller
 * @summary test
 * @request POST:/darkpar/common/queryTest/test
 */
export const commonQueryTestTest = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test',
    method: 'post',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request GET:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'get',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request HEAD:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'head',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request POST:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'post',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request PUT:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'put',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request DELETE:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'delete',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request OPTIONS:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'options',
    data: paramMap,
    instanceName: 'test',
  });
};

/**
 * @tags dkp-common-query-test-controller
 * @summary test2
 * @request PATCH:/darkpar/common/queryTest/test2
 */
export const commonQueryTestTest2 = (paramMap: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/darkpar/common/queryTest/test2',
    method: 'patch',
    data: paramMap,
    instanceName: 'test',
  });
};
