import {
  AptamilBiSku,
  AptamilClickResultVo,
  AptamilConfigRequest,
  AptamilLiveCreateOrUpdateRequest,
  AptamilLiveCreateOrUpdateResponse,
  AptamilMemberPointExcangeDataRequest,
  AptamilMemberPointExchangeAddRequest,
  AptamilMemberPointExchangeConditionAddRequest,
  AptamilMemberPointExchangeDeletePrizeRequest,
  AptamilMemberRequest,
  AptamilMemberResultVo,
  BrandBdCrmGiftInfo,
  Dz1848177292955844610DataRequest,
  Dz1848177292955844610DataResponse,
  Dz1848177292955844610DataSonActResponse,
  Dz1862304593288990722DataRequest,
  Dz1862304593288990722DataResponse,
  Dz1862304593288990722DataSonActResponse,
  ExportReportUsingPost1Params,
  ExportReportUsingPost2Params,
  GetAptamilRequest2Params,
  GetAptamilRequestParams,
  IPageCustomizedBdActivityAptamilMemberPointExchangeUserPrizeExportVo,
  UpLoadCardPwdUsingPostParams,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 爱他美-小转大有礼数据看板
 * @summary 获取数据
 * @request POST:/aptamil/aragraph/data/getDataPage
 */
export const aragraphDataGetDataPage = (
  request: Dz1848177292955844610DataRequest,
): Promise<Dz1848177292955844610DataResponse> => {
  return httpRequest({
    url: '/aptamil/aragraph/data/getDataPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美-小转大有礼数据看板
 * @summary 获取子活动列表
 * @request GET:/aptamil/aragraph/data/getSonActList
 */
export const aragraphDataGetSonActList = (
  query: GetAptamilRequestParams,
): Promise<Dz1848177292955844610DataSonActResponse[]> => {
  return httpRequest({
    url: '/aptamil/aragraph/data/getSonActList',
    method: 'get',
    params: query,
  });
};

/**
 * @tags 爱他美-小转大有礼数据看板
 * @summary 导出数据
 * @request POST:/aptamil/aragraph/data/report/export
 */
export const aragraphDataReportExport = (query: ExportReportUsingPost1Params): Promise<void> => {
  return httpRequest({
    url: '/aptamil/aragraph/data/report/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 下载模板
 * @request POST:/aptamil/config/aptamil/config/cardPwd/export
 */
export const configAptamilConfigCardPwdExport = (request: AptamilConfigRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/aptamil/config/cardPwd/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary BI人群点击导出
 * @request POST:/aptamil/config/biClickData/export
 */
export const configBiClickDataExport = (request: AptamilMemberRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/biClickData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary BI人群pvuv导出
 * @request POST:/aptamil/config/biPvUvData/export
 */
export const configBiPvUvDataExport = (request: AptamilMemberRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/biPvUvData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 删除
 * @request POST:/aptamil/config/deleteData
 */
export const configDeleteData = (request: BrandBdCrmGiftInfo): Promise<boolean> => {
  return httpRequest({
    url: '/aptamil/config/deleteData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 导出剩余
 * @request POST:/aptamil/config/exportRemainder/export
 */
export const configExportRemainderExport = (request: BrandBdCrmGiftInfo): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/exportRemainder/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary BI人群点击页面
 * @request POST:/aptamil/config/getBiClickInfo
 */
export const configGetBiClickInfo = (request: AptamilMemberRequest): Promise<AptamilClickResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getBiClickInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary BI人群pvuv页面
 * @request POST:/aptamil/config/getBiPvUvInfo
 */
export const configGetBiPvUvInfo = (request: AptamilMemberRequest): Promise<AptamilMemberResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getBiPvUvInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 实物奖品列表
 * @request POST:/aptamil/config/getGiftInfo
 */
export const configGetGiftInfo = (request: AptamilConfigRequest): Promise<AptamilMemberResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getGiftInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 等级积分兑换模块奖品发放情况
 * @request POST:/aptamil/config/getPointExchangePrizeData
 */
export const configGetPointExchangePrizeData = (
  request: AptamilMemberPointExcangeDataRequest,
): Promise<IPageCustomizedBdActivityAptamilMemberPointExchangeUserPrizeExportVo> => {
  return httpRequest({
    url: '/aptamil/config/getPointExchangePrizeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 积分兑换奖品发放情况
 * @request POST:/aptamil/config/getPrizeData
 */
export const configGetPrizeData = (request: AptamilMemberRequest): Promise<AptamilMemberResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getPrizeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 获取爱他美pvuv数据
 * @request POST:/aptamil/config/getPvUvData
 */
export const configGetPvUvData = (): Promise<AptamilMemberResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getPvUvData',
    method: 'post',
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 会员当日数据一览
 * @request POST:/aptamil/config/getPvUvDataByDay
 */
export const configGetPvUvDataByDay = (request: AptamilMemberRequest): Promise<AptamilMemberResultVo> => {
  return httpRequest({
    url: '/aptamil/config/getPvUvDataByDay',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary BiSku信息查询
 * @request POST:/aptamil/config/getSkusInfo
 */
export const configGetSkusInfo = (): Promise<AptamilBiSku> => {
  return httpRequest({
    url: '/aptamil/config/getSkusInfo',
    method: 'post',
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 积分兑换奖品发放情况导出
 * @request POST:/aptamil/config/memberData/export
 */
export const configMemberDataExport = (request: AptamilMemberRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/memberData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 等级积分兑换模块奖品发放情况导出
 * @request POST:/aptamil/config/pointExchangePrizeData/export
 */
export const configPointExchangePrizeDataExport = (request: AptamilMemberPointExcangeDataRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/pointExchangePrizeData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 会员当日数据一览导出
 * @request POST:/aptamil/config/pvUvData/export
 */
export const configPvUvDataExport = (request: AptamilMemberRequest): Promise<void> => {
  return httpRequest({
    url: '/aptamil/config/pvUvData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 保存实物奖品
 * @request POST:/aptamil/config/saveGiftInfo
 */
export const configSaveGiftInfo = (request: BrandBdCrmGiftInfo): Promise<boolean> => {
  return httpRequest({
    url: '/aptamil/config/saveGiftInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 保存爆款商品配置
 * @request POST:/aptamil/config/saveSkusInfo
 */
export const configSaveSkusInfo = (request: AptamilBiSku): Promise<boolean> => {
  return httpRequest({
    url: '/aptamil/config/saveSkusInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 置顶
 * @request POST:/aptamil/config/topping
 */
export const configTopping = (request: BrandBdCrmGiftInfo): Promise<boolean> => {
  return httpRequest({
    url: '/aptamil/config/topping',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心B端配置
 * @summary 上传卡密
 * @request POST:/aptamil/config/upLoadCardPwd
 */
export const configUpLoadCardPwd = (query: UpLoadCardPwdUsingPostParams, file: any): Promise<boolean> => {
  return httpRequest({
    url: '/aptamil/config/upLoadCardPwd',
    method: 'post',
    params: query,
    data: file,
  });
};

/**
 * @tags 爱他美直播间抽奖
 * @summary 创建活动
 * @request POST:/aptamil/live/createActivity
 */
export const liveCreateActivity = (
  request: AptamilLiveCreateOrUpdateRequest,
): Promise<AptamilLiveCreateOrUpdateResponse> => {
  return httpRequest({
    url: '/aptamil/live/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美直播间抽奖
 * @summary 修改活动
 * @request POST:/aptamil/live/updateActivity
 */
export const liveUpdateActivity = (
  request: AptamilLiveCreateOrUpdateRequest,
): Promise<AptamilLiveCreateOrUpdateResponse> => {
  return httpRequest({
    url: '/aptamil/live/updateActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心积分兑换
 * @summary 添加积分兑换
 * @request POST:/aptamil/memberPointExchange/addPointExchange
 */
export const memberPointExchangeAddPointExchange = (
  request: AptamilMemberPointExchangeAddRequest,
): Promise<AptamilMemberPointExchangeConditionAddRequest[]> => {
  return httpRequest({
    url: '/aptamil/memberPointExchange/addPointExchange',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心积分兑换
 * @summary 删除积分兑换
 * @request POST:/aptamil/memberPointExchange/deletePrize
 */
export const memberPointExchangeDeletePrize = (
  request: AptamilMemberPointExchangeDeletePrizeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/aptamil/memberPointExchange/deletePrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美会员中心积分兑换
 * @summary 获取积分兑换
 * @request POST:/aptamil/memberPointExchange/getPointExchange
 */
export const memberPointExchangeGetPointExchange = (): Promise<AptamilMemberPointExchangeConditionAddRequest[]> => {
  return httpRequest({
    url: '/aptamil/memberPointExchange/getPointExchange',
    method: 'post',
  });
};

/**
 * @tags 爱他美会员中心积分兑换
 * @summary 获取积分兑换规则
 * @request POST:/aptamil/memberPointExchange/getPointExchangeRule
 */
export const memberPointExchangeGetPointExchangeRule = (): Promise<string> => {
  return httpRequest({
    url: '/aptamil/memberPointExchange/getPointExchangeRule',
    method: 'post',
  });
};

/**
 * @tags 爱他美-转段有礼数据看板
 * @summary 获取数据
 * @request POST:/aptamil/turnSection/data/getDataPage
 */
export const turnSectionDataGetDataPage = (
  request: Dz1862304593288990722DataRequest,
): Promise<Dz1862304593288990722DataResponse> => {
  return httpRequest({
    url: '/aptamil/turnSection/data/getDataPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美-转段有礼数据看板
 * @summary 获取子活动列表
 * @request GET:/aptamil/turnSection/data/getSonActList
 * @originalName GetAptamilRequest
 * @duplicate
 */
export const turnSectionDataGetSonActList = (
  query: GetAptamilRequest2Params,
): Promise<Dz1862304593288990722DataSonActResponse[]> => {
  return httpRequest({
    url: '/aptamil/turnSection/data/getSonActList',
    method: 'get',
    params: query,
  });
};

/**
 * @tags 爱他美-转段有礼数据看板
 * @summary 导出数据
 * @request POST:/aptamil/turnSection/data/report/export
 */
export const turnSectionDataReportExport = (query: ExportReportUsingPost2Params): Promise<void> => {
  return httpRequest({
    url: '/aptamil/turnSection/data/report/export',
    method: 'post',
    params: query,
  });
};
