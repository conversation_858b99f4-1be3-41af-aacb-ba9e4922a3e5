import {
  CustomizedBdVivoHappyMomentsWorksExamine,
  Dz1874673979168907266DataRequest,
  Dz1874673979168907266DataResponse,
  Dz1874673979168907266ExamineCategoryResponse,
  Dz1874673979168907266ExamineDeleteRequest,
  Dz1874673979168907266ExamineListQueryRequest,
  Dz1874673979168907266ExamineQueryRequest,
  Dz1874673979168907266ExamineWorkRequest,
  Dz1874673979168907266RankPublishRequest,
  Dz1874673979168907266RankWorkModifyRequest,
  ExportReportUsingPost3Params,
  IPageDz1874673979168907266ExamineWorksResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 添加到待发布
 * @request POST:/vivo/examine/addPublish
 */
export const examineAddPublish = (request: Dz1874673979168907266ExamineWorkRequest): Promise<void> => {
  return httpRequest({
    url: '/vivo/examine/addPublish',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 榜单分类列表
 * @request POST:/vivo/examine/categoryList
 */
export const examineCategoryList = (): Promise<Dz1874673979168907266ExamineCategoryResponse[]> => {
  return httpRequest({
    url: '/vivo/examine/categoryList',
    method: 'post',
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 移除待发布作品
 * @request POST:/vivo/examine/delete
 */
export const examineDelete = (request: Dz1874673979168907266ExamineDeleteRequest): Promise<void> => {
  return httpRequest({
    url: '/vivo/examine/delete',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 获取数据
 * @request POST:/vivo/examine/getDataPage
 */
export const examineGetDataPage = (
  request: Dz1874673979168907266DataRequest,
): Promise<Dz1874673979168907266DataResponse> => {
  return httpRequest({
    url: '/vivo/examine/getDataPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 发布待审核作品上线
 * @request POST:/vivo/examine/publish
 */
export const examinePublish = (request: Dz1874673979168907266RankPublishRequest): Promise<void> => {
  return httpRequest({
    url: '/vivo/examine/publish',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 待发布作品列表
 * @request POST:/vivo/examine/rankWorksPage
 */
export const examineRankWorksPage = (
  request: Dz1874673979168907266ExamineListQueryRequest,
): Promise<CustomizedBdVivoHappyMomentsWorksExamine[]> => {
  return httpRequest({
    url: '/vivo/examine/rankWorksPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 导出数据
 * @request POST:/vivo/examine/report/export
 */
export const examineReportExport = (query: ExportReportUsingPost3Params): Promise<void> => {
  return httpRequest({
    url: '/vivo/examine/report/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 确认排名
 * @request POST:/vivo/examine/verifyRank
 */
export const examineVerifyRank = (request: Dz1874673979168907266RankWorkModifyRequest): Promise<void> => {
  return httpRequest({
    url: '/vivo/examine/verifyRank',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-定格幸福年审核看板
 * @summary 作品列表
 * @request POST:/vivo/examine/worksPage
 */
export const examineWorksPage = (
  request: Dz1874673979168907266ExamineQueryRequest,
): Promise<IPageDz1874673979168907266ExamineWorksResponse> => {
  return httpRequest({
    url: '/vivo/examine/worksPage',
    method: 'post',
    data: request,
  });
};
