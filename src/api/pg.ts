import {
  ExportReportUsingPost6Params,
  IPageLinkedHashMapStringObject,
  PgMemberDataColumnVO,
  PgMemberDataRequest,
  ReportColumnUsingPostParams,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 宝洁会员中心B端数据看板
 * @summary exportReport
 * @request POST:/pg/data/report/export
 */
export const dataReportExport = (query: ExportReportUsingPost6Params): Promise<void> => {
  return httpRequest({
    url: '/pg/data/report/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 宝洁会员中心B端数据看板
 * @summary reportColumn
 * @request POST:/pg/data/reportColumn
 */
export const dataReportColumn = (query: ReportColumnUsingPostParams): Promise<PgMemberDataColumnVO[]> => {
  return httpRequest({
    url: '/pg/data/reportColumn',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 宝洁会员中心B端数据看板
 * @summary reportPage
 * @request POST:/pg/data/reportPage
 */
export const dataReportPage = (request: PgMemberDataRequest): Promise<IPageLinkedHashMapStringObject> => {
  return httpRequest({
    url: '/pg/data/reportPage',
    method: 'post',
    data: request,
  });
};
