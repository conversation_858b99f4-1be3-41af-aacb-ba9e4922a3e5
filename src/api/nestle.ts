import {
  ActivityMcpd1000003112DataRequest,
  ActivityMcpd1000003112DataResponse,
  ActivityMcpd1000003112RecordRequest,
  ActivityMcpd1000003112Request,
  ActivityMcpd1000003112Response,
  ExportRecordReportUsingPostParams,
  ExportReportUsingPostParams,
  IPageActivityMcpd1000003112RecordResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 雀巢母婴会员装修
 * @summary 查询装修信息
 * @request POST:/nestle/member/decorate
 */
export const memberDecorate = (): Promise<ActivityMcpd1000003112Response> => {
  return httpRequest({
    url: '/nestle/member/decorate',
    method: 'post',
  });
};

/**
 * @tags 雀巢母婴会员装修
 * @summary 删除装修模块
 * @request POST:/nestle/member/delete
 */
export const memberDelete = (activityMcpd1000003112Request: ActivityMcpd1000003112Request): Promise<void> => {
  return httpRequest({
    url: '/nestle/member/delete',
    method: 'post',
    data: activityMcpd1000003112Request,
  });
};

/**
 * @tags 雀巢母婴会员装修
 * @summary 修改装修模块
 * @request POST:/nestle/member/update
 */
export const memberUpdate = (activityMcpd1000003112Request: ActivityMcpd1000003112Request): Promise<void> => {
  return httpRequest({
    url: '/nestle/member/update',
    method: 'post',
    data: activityMcpd1000003112Request,
  });
};

/**
 * @tags 雀巢母婴会员中心数据
 * @summary 数据报表查询
 * @request POST:/nestle/memberData/getDataPage
 */
export const memberDataGetDataPage = (
  request: ActivityMcpd1000003112DataRequest,
): Promise<ActivityMcpd1000003112DataResponse> => {
  return httpRequest({
    url: '/nestle/memberData/getDataPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢母婴会员中心数据
 * @summary 领奖记录查询
 * @request POST:/nestle/memberData/getRecordPage
 */
export const memberDataGetRecordPage = (
  request: ActivityMcpd1000003112RecordRequest,
): Promise<IPageActivityMcpd1000003112RecordResponse> => {
  return httpRequest({
    url: '/nestle/memberData/getRecordPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雀巢母婴会员中心数据
 * @summary 数据报表导出
 * @request POST:/nestle/memberData/reportData/export
 */
export const memberDataReportDataExport = (query: ExportReportUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/nestle/memberData/reportData/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 雀巢母婴会员中心数据
 * @summary 数据报表导出
 * @request POST:/nestle/memberData/reportRecord/export
 */
export const memberDataReportRecordExport = (query: ExportRecordReportUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/nestle/memberData/reportRecord/export',
    method: 'post',
    params: query,
  });
};
