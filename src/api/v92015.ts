import { Activity92015CreateOrUpdateRequest, Activity92015CreateOrUpdateResponse, Activity92015SkuVo } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 澳优定制-集罐有礼
 * @summary 创建活动
 * @request POST:/92015/createActivity
 */
export const createActivity = (
  request: Activity92015CreateOrUpdateRequest,
): Promise<Activity92015CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92015/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 澳优定制-集罐有礼
 * @summary 导入系列信息excel
 * @request POST:/92015/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity92015SkuVo[]> => {
  return httpRequest({
    url: '/92015/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 澳优定制-集罐有礼
 * @summary 下载模板
 * @request POST:/92015/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/92015/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 澳优定制-集罐有礼
 * @summary 修改活动
 * @request POST:/92015/updateActivity
 */
export const updateActivity = (
  request: Activity92015CreateOrUpdateRequest,
): Promise<Activity92015CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92015/updateActivity',
    method: 'post',
    data: request,
  });
};
