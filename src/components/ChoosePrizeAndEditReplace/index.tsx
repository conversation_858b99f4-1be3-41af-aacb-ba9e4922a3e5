/**
 * Author: zhangyue
 * Date: 2023-06-08 14:58
 * Description:
 */

import React, { ReactNode, useEffect, useState } from 'react';
import { Button, Radio } from '@alifd/next';
import JdCoupon from './JdCoupon';
import JdBean from './JdBean';
import JdProduct from './JdProduct';
import JdGiftCard from './JdGiftCard';
import { hasPrizeType } from '@/api/prize';
import LzTipPanel from '@/components/LzTipPanel';
import { GetJdAllTypeResponse } from '@/api/types';
import { activityEditDisabled, deepCopy } from '@/utils';

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  originalPrize: number;
  discountedPrize: number;
  skuList: any[];
  tokenPrize: any[];
}

interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

interface ChoosePrizeProps {
  editValue: PrizeInfo | null;
  editOldValue: PrizeInfo | null;
  onChange: (data: PrizeInfo) => void;
  onCancel: () => void;
  hasProbability?: boolean;
  hasLimit?: boolean;
  hasOrderPrice?: boolean;
  productImgTip?: boolean;
  typeList?: number[];
  planList?: any[];
  width?: number;
  height?: number;
  prizeNameLength?: number;
  defaultTarget?: number;
  formData: any;
  sendTotalCountMin?: number;
  sendTotalCountMax?: number;
  isBosideng?: boolean;
  hasEditPrize?: boolean;
  hasEditPrizeType?: boolean;
}

export const prizeFormLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};
export default ({
  editValue,
  editOldValue,
  hasEditPrize = true,
  hasEditPrizeType = true,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  productImgTip = false,
  typeList = [1, 2, 3, 4, 6, 7, 8, 9, 10, 12],
  planList,
  width = 200,
  height = 200,
  prizeNameLength = 10,
  defaultTarget = 12,
  formData,
  sendTotalCountMin = 1,
  sendTotalCountMax = 999999999,
  isBosideng = false,
}: ChoosePrizeProps) => {
  const [target, setTarget] = useState(defaultTarget);
  const [edit, setEdit] = useState(editValue);
  const [hasProperty, setHasProperty] = useState(true);
  formData.prizeList = formData?.prizeList || [];
  const [newFormData, setNewFormData] = useState(deepCopy(formData));

  const eventProps = {
    editValue: edit,
    editOldValue,
    onChange,
    onCancel,
    hasProbability,
    hasLimit,
    hasOrderPrice,
    productImgTip,
    planList,
    width,
    height,
    prizeNameLength,
    formData: newFormData,
    sendTotalCountMin,
    sendTotalCountMax,
    hasEditPrize,
    hasEditPrizeType,
  };

  interface ComponentMap {
    [key: number]: ReactNode;
  }

  interface Option {
    label: string;
    value: number;
  }

  const componentMap: ComponentMap = {
    1: <JdCoupon {...eventProps} />,
    2: <JdBean {...eventProps} />,
    3: <JdProduct {...eventProps} />,
    7: <JdGiftCard {...eventProps} />,
  };

  const options: Option[] = [
    { label: '京元宝权益', value: 12 },
    { label: '京豆', value: 2 },
    { label: isBosideng ? '优惠券包' : '优惠券', value: 1 },
    { label: '实物', value: 3 },
    { label: '积分', value: 4 },
    { label: '红包', value: 6 },
    { label: '礼品卡', value: 7 },
    { label: '京东E卡', value: 8 },
    { label: 'PLUS会员卡', value: 9 },
    { label: '爱奇艺会员卡', value: 10 },
    { label: '令牌', value: 11 },
  ];

  const TIP = (
    <div style={{ fontSize: '12px', color: 'black' }}>
      <div>当前资产无法访问，建议解决方法如下:</div>
      <div>1. 若店铺还未在京东平台开通此资产服务，需前往京东后台开通后再尝试</div>
      <div style={{ display: 'flex', justifyContent: 'space-around' }}>
        <Button
          type="primary"
          text
          onClick={() => {
            window.open('https://helpcenter.jd.com/vender/issue/937-5150.html', '_blank');
          }}
        >
          查看操作指南
        </Button>
        <Button
          type="primary"
          text
          onClick={() => {
            window.open('http://quanyi.shop.jd.com/#/assetList', '_blank');
          }}
        >
          前往开通
        </Button>
      </div>
      <div>2. 若店铺已在京东平台开通此资产服务，请使用店铺主账号从京麦工作台进入云鹿一次，然后再使用其他账号尝试</div>
    </div>
  );

  const checkPrizeType = (value: string): void => {
    hasPrizeType({ prizeType: value })
      .then((res: GetJdAllTypeResponse[]) => {
        if (res) {
          setHasProperty(true);
        } else {
          setHasProperty(false);
        }
      })
      .catch(() => {
        setHasProperty(false);
      });
  };
  useEffect(() => {
    if (editValue) {
      const _formData = deepCopy(formData);
      _formData.totalProbability -= Number(editValue.probability);
      setNewFormData(_formData);
    }
    editValue && setTarget(editValue.prizeType || target);
    checkPrizeType('12');
  }, []);
  return (
    <div>
      {((activityEditDisabled() && hasEditPrizeType) || !activityEditDisabled()) && (
        <Radio.Group
          defaultValue={target}
          value={target}
          shape="button"
          onChange={(value: number): void => {
            if (editValue && editValue.prizeType === value) {
              setEdit(editValue);
            } else {
              setEdit(null);
            }
            setTarget(value);
            checkPrizeType(String(value));
          }}
          style={{ marginTop: '16px' }}
        >
          {options.map((option) => {
            if (typeList.includes(option.value)) {
              return (
                <Radio key={option.value} value={option.value}>
                  {option.label}
                </Radio>
              );
            }
            return null;
          })}
        </Radio.Group>
      )}
      {!hasProperty && (
        <div style={{ marginTop: '15px' }}>
          <LzTipPanel message={TIP} warning />
        </div>
      )}
      {hasProperty && (
        <div>
          <div style={{ color: 'red', marginTop: '15px' }}>
            重要提示：除积分外，在【不同互动活动】或【同一活动的不同获奖门槛】配置同一奖品类型时，需要选择完全不同的奖品权益计划，以避免因多次使用同一权益计划而造成活动奖品提前发光而引起的客诉。
          </div>
          {typeList.includes(target) && componentMap[target]}
        </div>
      )}
    </div>
  );
};
