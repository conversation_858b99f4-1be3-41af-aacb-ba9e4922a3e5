body {
  font-family: -apple-system, <PERSON>Fang SC, Microsoft YaHei, sans-serif, serif;
  -webkit-font-smoothing: antialiased;
  background-color: #f1f4f8; // 给定的默认背景色
  font-size: 12px;
}

//.next-shell-sub-main {
//  overflow: unset!important;
//}
@import 'assets/style/coupou';

.crm-interaction-container {
  @import 'assets/style/interaction';
  min-width: 960px;
  margin: 20px;
}

.crm-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  height: 16px;
  line-height: 16px;
  -webkit-background-clip: text;
  border-left: 3px solid #39f;
  padding-left: 8px;
  margin-bottom: 25px;
}

.crm-container {
  padding-bottom: 50px;
}

.crm-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  display: flex;
  justify-content: center;
  align-items: center;

  button {
    margin-right: 10px;
  }
}

.crm-warning {
  color: red;
  margin-top: 15px;
}

.w-300 {
  width: 300px !important;
}

.image-preview {
  position: relative;
  width: 100px;
  height: 100px;
  // border: 1px solid $LIGHT_GRAY;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;

  .del-box {
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
  }

  &:hover .del-box {
    display: flex;
  }
}

.validateInput {
  height: 0 !important;
  border: none;
  outline: none;
  color: transparent;
}

.validateInput input {
  height: 0 !important;
  color: white !important;
  -webkit-text-fill-color: transparent !important;
}

.next-card {
  box-shadow: none !important;
}

.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.next-form-item-extra {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.5;
  color: #8d9299;
}
