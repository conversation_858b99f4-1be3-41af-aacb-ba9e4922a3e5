module.exports = {
  define: {
    env: process.env.NODE_ENV,
  },
  vite: false,
  hash: true,
  minify: false,
  publicPath: '/custom/',
  devPublicPath: 'http://127.0.0.1:2002/',
  outputDir: 'dist',
  eslint: false,
  outputAssetsPath: {
    js: 'js',
    css: 'css',
  },
  // externals: {
  //   react: 'React',
  //   'react-dom': 'ReactDOM',
  //   '@alifd/next': 'Next',
  // },
  plugins: [
    [
      'build-plugin-icestark',
      {
        umd: true,
        type: 'child',
        uniqueName: 'custom',
      },
    ],
    './build.plugin.js',
    './webpack.config.js',
  ],
  proxy: {
    '/apps/custom/api': {
      enable: true,
      target: 'https://crmbsaas-test.dianpusoft.cn/apps/custom/api',
      // target: 'http://192.168.1.253:8082',
      changeOrigin: true,
      pathRewrite: {
        '^/apps/custom/api': '',
      },
    },
  },
};
